"""
Gateway Middleware Module

Contains middleware for request processing, rate limiting, CORS, and monitoring.

@Author: Enhanced by AI Assistant
"""

import time
import json
from typing import Callable, Dict, Any
from collections import defaultdict, deque

from fastapi import Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.routing import APIRouter
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from core.config.app_logger import logger
from .config import gateway_config
from .exceptions import RateLimitExceededError, RequestValidationError
from .utils import generate_request_id, sanitize_headers, format_duration


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for request/response logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate request ID
        request_id = generate_request_id()
        request.state.request_id = request_id
        
        # Start timing
        start_time = time.time()
        
        # Log request
        if gateway_config.monitoring.log_requests:
            self._log_request(request, request_id)
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log response
            if gateway_config.monitoring.log_responses:
                self._log_response(request, response, request_id, duration)
            
            # Add headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Response-Time"] = f"{duration:.3f}s"
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(
                f"Request failed: {str(e)}",
                extra={
                    "request_id": request_id,
                    "method": request.method,
                    "path": request.url.path,
                    "duration": duration,
                    "error_type": type(e).__name__
                }
            )
            raise
    
    def _log_request(self, request: Request, request_id: str):
        """Log incoming request."""
        headers = dict(request.headers)
        if not gateway_config.monitoring.log_headers:
            headers = sanitize_headers(headers)
        
        logger.info(
            f"Incoming request: {request.method} {request.url.path}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "headers": headers,
                "client_ip": request.client.host if request.client else None,
                "user_agent": request.headers.get("user-agent")
            }
        )
    
    def _log_response(self, request: Request, response: Response, request_id: str, duration: float):
        """Log outgoing response."""
        logger.info(
            f"Response: {response.status_code} for {request.method} {request.url.path}",
            extra={
                "request_id": request_id,
                "status_code": response.status_code,
                "duration": duration,
                "duration_formatted": format_duration(duration)
            }
        )


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware for rate limiting."""
    
    def __init__(self, app):
        super().__init__(app)
        self.request_counts: Dict[str, deque] = defaultdict(lambda: deque())
        self.config = gateway_config.rate_limit
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if not self.config.enabled:
            return await call_next(request)
        
        # Get rate limit key
        key = self._get_rate_limit_key(request)
        
        # Check rate limit
        if self._is_rate_limited(key):
            logger.warning(
                f"Rate limit exceeded for key: {key}",
                extra={
                    "rate_limit_key": key,
                    "method": request.method,
                    "path": request.url.path,
                    "client_ip": request.client.host if request.client else None
                }
            )
            
            return JSONResponse(
                status_code=429,
                content={
                    "error": {
                        "code": 429,
                        "message": "Rate limit exceeded",
                        "details": {
                            "limit": self.config.requests_per_minute,
                            "window": "1 minute"
                        }
                    }
                }
            )
        
        # Record request
        self._record_request(key)
        
        return await call_next(request)
    
    def _get_rate_limit_key(self, request: Request) -> str:
        """Get rate limiting key based on configuration."""
        if self.config.key_func == "ip":
            return request.client.host if request.client else "unknown"
        elif self.config.key_func == "user":
            if hasattr(request.state, "user") and request.state.user:
                return f"user:{request.state.user.get('userId', 'unknown')}"
            return request.client.host if request.client else "unknown"
        else:
            # Default to IP
            return request.client.host if request.client else "unknown"
    
    def _is_rate_limited(self, key: str) -> bool:
        """Check if the key is rate limited."""
        current_time = time.time()
        window_start = current_time - 60  # 1 minute window
        
        # Clean old requests
        request_times = self.request_counts[key]
        while request_times and request_times[0] < window_start:
            request_times.popleft()
        
        # Check if limit exceeded
        return len(request_times) >= self.config.requests_per_minute
    
    def _record_request(self, key: str):
        """Record a request for rate limiting."""
        self.request_counts[key].append(time.time())


class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware for security checks."""
    
    def __init__(self, app):
        super().__init__(app)
        self.config = gateway_config.security
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Check request size
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.config.max_request_size:
            logger.warning(
                f"Request size too large: {content_length} bytes",
                extra={
                    "content_length": content_length,
                    "max_size": self.config.max_request_size,
                    "method": request.method,
                    "path": request.url.path
                }
            )
            
            return JSONResponse(
                status_code=413,
                content={
                    "error": {
                        "code": 413,
                        "message": "Request entity too large",
                        "details": {
                            "max_size": self.config.max_request_size,
                            "received_size": int(content_length)
                        }
                    }
                }
            )
        
        # Check blocked IPs
        client_ip = request.client.host if request.client else None
        if client_ip and client_ip in self.config.blocked_ips:
            logger.warning(
                f"Blocked IP attempted access: {client_ip}",
                extra={
                    "blocked_ip": client_ip,
                    "method": request.method,
                    "path": request.url.path
                }
            )
            
            return JSONResponse(
                status_code=403,
                content={
                    "error": {
                        "code": 403,
                        "message": "Access denied"
                    }
                }
            )
        
        # Check User-Agent if configured
        if self.config.allowed_user_agents:
            user_agent = request.headers.get("user-agent", "")
            if not any(allowed in user_agent for allowed in self.config.allowed_user_agents):
                logger.warning(
                    f"Blocked user agent: {user_agent}",
                    extra={
                        "user_agent": user_agent,
                        "method": request.method,
                        "path": request.url.path
                    }
                )
                
                return JSONResponse(
                    status_code=403,
                    content={
                        "error": {
                            "code": 403,
                            "message": "Access denied"
                        }
                    }
                )
        
        return await call_next(request)


def setup_gateway_middleware(router: APIRouter):
    """Setup middleware for the gateway router."""
    
    # Note: FastAPI router doesn't support middleware directly
    # These would typically be added to the main FastAPI app
    # This function serves as documentation for the middleware setup
    
    logger.info("Gateway middleware configuration documented")
    
    # CORS middleware would be added to the main app like this:
    # app.add_middleware(
    #     CORSMiddleware,
    #     allow_origins=gateway_config.security.cors_origins,
    #     allow_credentials=True,
    #     allow_methods=gateway_config.security.cors_methods,
    #     allow_headers=gateway_config.security.cors_headers,
    # )
    
    # Custom middleware would be added like this:
    # app.add_middleware(RequestLoggingMiddleware)
    # app.add_middleware(RateLimitMiddleware)
    # app.add_middleware(SecurityMiddleware)
