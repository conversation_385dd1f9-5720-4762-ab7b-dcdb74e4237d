# 消息评价功能实现文档

## 功能概述

为消息系统添加了同意、反对的评价功能，用户可以对 AI 生成的消息进行评价，系统会统计同意和反对的数量。

## 实现的功能

### 1. 数据库字段扩展

在 `message` 表中添加了以下字段：
- `like_count`: 同意数量 (整数，默认0)
- `dislike_count`: 反对数量 (整数，默认0)  
- `user_rating`: 用户评价状态 (字符串，可选值: "like", "dislike", null)

### 2. 数据库迁移

- 创建了迁移文件 `884cb900b6d2_add_message_rating_fields.py`
- 使用 `server_default='0'` 确保现有数据的兼容性
- 已成功应用到数据库

### 3. CRUD 操作扩展

在 `CRUDMessage` 类中添加了两个新方法：

#### `rate_message()`
- 对消息进行评价
- 支持同意 ("like")、反对 ("dislike")、取消评价 (None)
- 智能处理评价状态变更，确保计数准确
- 防止计数为负数

#### `get_message_rating()`
- 获取消息的评价信息
- 返回包含 message_id, like_count, dislike_count, user_rating 的字典

### 4. API 接口

添加了两个新的 REST API 端点：

#### POST `/api/message/{message_id}/rate`
对消息进行评价

**请求参数:**
- `message_id`: 路径参数，消息ID
- `rating`: 请求体，评价类型 ("like", "dislike", "")

**权限验证:**
- 需要用户认证
- 只能评价属于自己会话的消息

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "message_id": "xxx",
    "like_count": 1,
    "dislike_count": 0,
    "user_rating": "like"
  },
  "message": "评价成功"
}
```

#### GET `/api/message/{message_id}/rating`
获取消息评价信息

**请求参数:**
- `message_id`: 路径参数，消息ID

**权限验证:**
- 需要用户认证
- 只能查看属于自己会话的消息评价

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "message_id": "xxx",
    "like_count": 1,
    "dislike_count": 0,
    "user_rating": "like"
  },
  "message": "查询成功"
}
```

## 评价逻辑

### 评价状态转换
1. **无评价 → 同意**: like_count +1
2. **无评价 → 反对**: dislike_count +1
3. **同意 → 反对**: like_count -1, dislike_count +1
4. **反对 → 同意**: dislike_count -1, like_count +1
5. **同意 → 取消**: like_count -1
6. **反对 → 取消**: dislike_count -1

### 安全特性
- 计数不会变为负数 (使用 `max(0, count - 1)`)
- 权限验证确保用户只能操作自己的消息
- 输入验证确保评价类型正确

## 测试

### 单元测试
- `test_message_rating.py`: 测试 CRUD 操作的正确性
- 验证评价状态转换逻辑
- 验证计数准确性

### API 测试
- `test_api_rating.py`: 提供 API 测试框架和文档
- 包含完整的 API 使用示例

## 文件修改清单

### 新增文件
- `migrations/versions/884cb900b6d2_add_message_rating_fields.py` - 数据库迁移
- `test_message_rating.py` - 功能测试脚本
- `test_api_rating.py` - API 测试脚本
- `MESSAGE_RATING_FEATURE.md` - 功能文档

### 修改文件
- `core/services/database/schemas/message.py` - 添加评价字段
- `core/services/database/crud/message.py` - 添加评价方法
- `core/api/message.py` - 添加评价 API 接口

## 使用示例

### 前端集成示例

```javascript
// 对消息进行同意评价
async function likeMessage(messageId) {
  const response = await fetch(`/api/message/${messageId}/rate`, {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ rating: 'like' })
  });
  return response.json();
}

// 对消息进行反对评价
async function dislikeMessage(messageId) {
  const response = await fetch(`/api/message/${messageId}/rate`, {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ rating: 'dislike' })
  });
  return response.json();
}

// 获取消息评价信息
async function getMessageRating(messageId) {
  const response = await fetch(`/api/message/${messageId}/rating`, {
    headers: {
      'Authorization': 'Bearer ' + token
    }
  });
  return response.json();
}
```

## 后续扩展建议

1. **用户评价记录表**: 创建独立的用户评价记录表，支持多用户对同一消息的评价
2. **评价统计**: 添加评价统计分析功能
3. **评价通知**: 当消息收到评价时通知相关用户
4. **评价历史**: 记录评价历史和变更日志
5. **批量操作**: 支持批量评价操作

## 注意事项

1. 当前实现中，每个消息只记录一个用户的评价状态
2. 如需支持多用户评价，需要创建独立的评价记录表
3. 评价功能只对 AI 消息有效，人类消息通常不需要评价
4. 建议在前端添加防抖处理，避免用户快速点击导致的重复请求
