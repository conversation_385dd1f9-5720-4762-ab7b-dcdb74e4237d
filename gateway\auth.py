"""
Gateway Authentication Module

Enhanced authentication and authorization for the gateway.

@Author: Enhanced by AI Assistant
"""

import time
from typing import Optional, Dict, Any, List
from fastapi import Request, HTTPException

from core.config.app_logger import logger
from core.config.security import get_current_user
from core.vo.user_vo import User<PERSON>
from .config import gateway_config
from .exceptions import AuthenticationError, AuthorizationError


class GatewayAuthenticator:
    """Enhanced authentication for gateway requests."""
    
    def __init__(self):
        self.config = gateway_config.security
        self._auth_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_ttl = 300  # 5 minutes
    
    async def authenticate_request(self, request: Request) -> Optional[UserVO]:
        """
        Authenticate incoming request.
        
        Args:
            request: The incoming HTTP request
            
        Returns:
            UserVO object if authentication successful, None otherwise
            
        Raises:
            AuthenticationError: If authentication fails
        """
        if not self.config.require_auth:
            return None
        
        try:
            # Extract authorization header
            auth_header = request.headers.get("Authorization")
            if not auth_header:
                raise AuthenticationError("Authorization header is missing")
            
            # Check cache first
            cached_user = self._get_cached_user(auth_header)
            if cached_user:
                request.state.user = cached_user
                return cached_user
            
            # Authenticate using existing security module
            user = await get_current_user(auth_header)
            
            # Cache the user
            self._cache_user(auth_header, user)
            
            # Store user in request state
            request.state.user = user
            
            logger.debug(
                f"User authenticated: {user.username}",
                extra={
                    "user_id": user.userId,
                    "username": user.username,
                    "permissions": list(user.permissions) if user.permissions else []
                }
            )
            
            return user
            
        except HTTPException as e:
            logger.warning(
                f"Authentication failed: {e.detail}",
                extra={
                    "status_code": e.status_code,
                    "path": request.url.path,
                    "method": request.method,
                    "client_ip": request.client.host if request.client else None
                }
            )
            raise AuthenticationError(e.detail)
        
        except Exception as e:
            logger.error(
                f"Authentication error: {str(e)}",
                extra={
                    "error_type": type(e).__name__,
                    "path": request.url.path,
                    "method": request.method
                },
                exc_info=True
            )
            raise AuthenticationError("Authentication failed")
    
    def authorize_request(
        self, 
        request: Request, 
        required_permissions: Optional[List[str]] = None,
        required_roles: Optional[List[str]] = None
    ) -> bool:
        """
        Authorize request based on user permissions and roles.
        
        Args:
            request: The incoming HTTP request
            required_permissions: List of required permissions
            required_roles: List of required roles
            
        Returns:
            True if authorized, False otherwise
            
        Raises:
            AuthorizationError: If authorization fails
        """
        if not hasattr(request.state, "user") or not request.state.user:
            if self.config.require_auth:
                raise AuthorizationError("User not authenticated")
            return True
        
        user = request.state.user
        
        # Check permissions
        if required_permissions:
            user_permissions = set(user.permissions) if user.permissions else set()
            required_perms = set(required_permissions)
            
            if not required_perms.issubset(user_permissions):
                missing_perms = required_perms - user_permissions
                logger.warning(
                    f"Authorization failed - missing permissions: {missing_perms}",
                    extra={
                        "user_id": user.userId,
                        "username": user.username,
                        "required_permissions": required_permissions,
                        "user_permissions": list(user_permissions),
                        "missing_permissions": list(missing_perms)
                    }
                )
                raise AuthorizationError(f"Missing required permissions: {list(missing_perms)}")
        
        # Check roles (if implemented in UserVO)
        if required_roles:
            # Note: UserVO doesn't have roles field in the current implementation
            # This is a placeholder for future role-based authorization
            logger.debug("Role-based authorization not implemented yet")
        
        logger.debug(
            f"User authorized: {user.username}",
            extra={
                "user_id": user.userId,
                "username": user.username,
                "required_permissions": required_permissions,
                "required_roles": required_roles
            }
        )
        
        return True
    
    def _get_cached_user(self, auth_header: str) -> Optional[UserVO]:
        """Get user from cache if available and not expired."""
        cache_key = self._get_cache_key(auth_header)
        cached_data = self._auth_cache.get(cache_key)
        
        if not cached_data:
            return None
        
        # Check if cache is expired
        if time.time() - cached_data["timestamp"] > self._cache_ttl:
            del self._auth_cache[cache_key]
            return None
        
        return cached_data["user"]
    
    def _cache_user(self, auth_header: str, user: UserVO):
        """Cache user data."""
        cache_key = self._get_cache_key(auth_header)
        self._auth_cache[cache_key] = {
            "user": user,
            "timestamp": time.time()
        }
        
        # Clean up old cache entries
        self._cleanup_cache()
    
    def _get_cache_key(self, auth_header: str) -> str:
        """Generate cache key from auth header."""
        # Use hash of auth header for security
        import hashlib
        return hashlib.sha256(auth_header.encode()).hexdigest()
    
    def _cleanup_cache(self):
        """Clean up expired cache entries."""
        current_time = time.time()
        expired_keys = [
            key for key, data in self._auth_cache.items()
            if current_time - data["timestamp"] > self._cache_ttl
        ]
        
        for key in expired_keys:
            del self._auth_cache[key]
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get authentication cache statistics."""
        current_time = time.time()
        active_entries = sum(
            1 for data in self._auth_cache.values()
            if current_time - data["timestamp"] <= self._cache_ttl
        )
        
        return {
            "total_entries": len(self._auth_cache),
            "active_entries": active_entries,
            "cache_ttl": self._cache_ttl,
            "hit_rate": getattr(self, "_cache_hits", 0) / max(getattr(self, "_cache_requests", 1), 1)
        }


# Global authenticator instance
gateway_authenticator = GatewayAuthenticator()


async def authenticate_gateway_request(request: Request) -> Optional[UserVO]:
    """
    Convenience function for gateway request authentication.
    
    Args:
        request: The incoming HTTP request
        
    Returns:
        UserVO object if authentication successful, None otherwise
    """
    return await gateway_authenticator.authenticate_request(request)


def authorize_gateway_request(
    request: Request,
    required_permissions: Optional[List[str]] = None,
    required_roles: Optional[List[str]] = None
) -> bool:
    """
    Convenience function for gateway request authorization.
    
    Args:
        request: The incoming HTTP request
        required_permissions: List of required permissions
        required_roles: List of required roles
        
    Returns:
        True if authorized, False otherwise
    """
    return gateway_authenticator.authorize_request(
        request, required_permissions, required_roles
    )
