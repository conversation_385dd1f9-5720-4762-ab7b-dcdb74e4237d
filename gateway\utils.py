"""
Gateway Utilities Module

Contains utility classes for circuit breaker, metrics, and other helper functions.

@Author: Enhanced by AI Assistant
"""

import time
import uuid
from collections import defaultdict, deque
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

from core.config.app_logger import logger


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


@dataclass
class CircuitBreakerStats:
    """Circuit breaker statistics."""
    failure_count: int = 0
    success_count: int = 0
    last_failure_time: Optional[float] = None
    last_success_time: Optional[float] = None
    state_change_time: float = field(default_factory=time.time)


class CircuitBreaker:
    """Circuit breaker implementation for service resilience."""
    
    def __init__(
        self, 
        failure_threshold: int = 5, 
        timeout: float = 60.0,
        success_threshold: int = 3
    ):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.success_threshold = success_threshold
        self.state = CircuitBreakerState.CLOSED
        self.stats = CircuitBreakerStats()
        self._half_open_success_count = 0
    
    def is_open(self) -> bool:
        """Check if circuit breaker is open."""
        if self.state == CircuitBreakerState.OPEN:
            # Check if timeout has passed
            if time.time() - self.stats.state_change_time >= self.timeout:
                self._transition_to_half_open()
                return False
            return True
        return False
    
    def is_half_open(self) -> bool:
        """Check if circuit breaker is half-open."""
        return self.state == CircuitBreakerState.HALF_OPEN
    
    def record_success(self):
        """Record a successful operation."""
        self.stats.success_count += 1
        self.stats.last_success_time = time.time()
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            self._half_open_success_count += 1
            if self._half_open_success_count >= self.success_threshold:
                self._transition_to_closed()
        elif self.state == CircuitBreakerState.OPEN:
            # Should not happen, but handle gracefully
            self._transition_to_closed()
    
    def record_failure(self):
        """Record a failed operation."""
        self.stats.failure_count += 1
        self.stats.last_failure_time = time.time()
        
        if self.state == CircuitBreakerState.CLOSED:
            if self.stats.failure_count >= self.failure_threshold:
                self._transition_to_open()
        elif self.state == CircuitBreakerState.HALF_OPEN:
            self._transition_to_open()
    
    def _transition_to_open(self):
        """Transition to open state."""
        self.state = CircuitBreakerState.OPEN
        self.stats.state_change_time = time.time()
        self._half_open_success_count = 0
        logger.warning(f"Circuit breaker opened after {self.stats.failure_count} failures")
    
    def _transition_to_half_open(self):
        """Transition to half-open state."""
        self.state = CircuitBreakerState.HALF_OPEN
        self.stats.state_change_time = time.time()
        self._half_open_success_count = 0
        logger.info("Circuit breaker transitioned to half-open")
    
    def _transition_to_closed(self):
        """Transition to closed state."""
        self.state = CircuitBreakerState.CLOSED
        self.stats.state_change_time = time.time()
        self.stats.failure_count = 0  # Reset failure count
        self._half_open_success_count = 0
        logger.info("Circuit breaker closed")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics."""
        return {
            "state": self.state.value,
            "failure_count": self.stats.failure_count,
            "success_count": self.stats.success_count,
            "last_failure_time": self.stats.last_failure_time,
            "last_success_time": self.stats.last_success_time,
            "state_change_time": self.stats.state_change_time,
            "failure_threshold": self.failure_threshold,
            "timeout": self.timeout
        }


@dataclass
class RequestMetric:
    """Individual request metric."""
    timestamp: float
    service: str
    method: str
    status_code: int
    duration: float
    success: bool
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))


class RequestMetrics:
    """Request metrics collector."""
    
    def __init__(self, max_metrics: int = 10000):
        self.max_metrics = max_metrics
        self.metrics: deque = deque(maxlen=max_metrics)
        self.service_metrics: Dict[str, List[RequestMetric]] = defaultdict(list)
    
    def record_request(
        self,
        service: str,
        method: str,
        status_code: int,
        duration: float,
        success: bool
    ):
        """Record a request metric."""
        metric = RequestMetric(
            timestamp=time.time(),
            service=service,
            method=method,
            status_code=status_code,
            duration=duration,
            success=success
        )
        
        self.metrics.append(metric)
        self.service_metrics[service].append(metric)
        
        # Keep only recent metrics per service
        if len(self.service_metrics[service]) > 1000:
            self.service_metrics[service] = self.service_metrics[service][-1000:]
    
    def get_service_metrics(self, service: str, time_window: float = 300) -> Dict[str, Any]:
        """Get metrics for a specific service within a time window."""
        current_time = time.time()
        cutoff_time = current_time - time_window
        
        recent_metrics = [
            m for m in self.service_metrics[service]
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {
                "total_requests": 0,
                "success_rate": 0.0,
                "average_duration": 0.0,
                "error_rate": 0.0,
                "status_codes": {}
            }
        
        total_requests = len(recent_metrics)
        successful_requests = sum(1 for m in recent_metrics if m.success)
        total_duration = sum(m.duration for m in recent_metrics)
        
        status_codes = defaultdict(int)
        for metric in recent_metrics:
            status_codes[metric.status_code] += 1
        
        return {
            "total_requests": total_requests,
            "success_rate": successful_requests / total_requests if total_requests > 0 else 0.0,
            "error_rate": (total_requests - successful_requests) / total_requests if total_requests > 0 else 0.0,
            "average_duration": total_duration / total_requests if total_requests > 0 else 0.0,
            "status_codes": dict(status_codes),
            "time_window": time_window
        }
    
    def get_overall_metrics(self, time_window: float = 300) -> Dict[str, Any]:
        """Get overall metrics across all services."""
        current_time = time.time()
        cutoff_time = current_time - time_window
        
        recent_metrics = [
            m for m in self.metrics
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {
                "total_requests": 0,
                "success_rate": 0.0,
                "average_duration": 0.0,
                "services": {}
            }
        
        total_requests = len(recent_metrics)
        successful_requests = sum(1 for m in recent_metrics if m.success)
        total_duration = sum(m.duration for m in recent_metrics)
        
        services = set(m.service for m in recent_metrics)
        service_metrics = {
            service: self.get_service_metrics(service, time_window)
            for service in services
        }
        
        return {
            "total_requests": total_requests,
            "success_rate": successful_requests / total_requests if total_requests > 0 else 0.0,
            "average_duration": total_duration / total_requests if total_requests > 0 else 0.0,
            "services": service_metrics,
            "time_window": time_window
        }


def generate_request_id() -> str:
    """Generate a unique request ID."""
    return str(uuid.uuid4())


def sanitize_headers(headers: Dict[str, str]) -> Dict[str, str]:
    """Sanitize headers by removing sensitive information."""
    sensitive_headers = {
        "authorization", "cookie", "x-api-key", "x-auth-token",
        "x-access-token", "x-csrf-token", "x-session-id"
    }
    
    return {
        key: "***REDACTED***" if key.lower() in sensitive_headers else value
        for key, value in headers.items()
    }


def format_duration(duration: float) -> str:
    """Format duration in a human-readable format."""
    if duration < 1:
        return f"{duration * 1000:.2f}ms"
    elif duration < 60:
        return f"{duration:.2f}s"
    else:
        minutes = int(duration // 60)
        seconds = duration % 60
        return f"{minutes}m {seconds:.2f}s"
