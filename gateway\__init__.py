"""
Enhanced Gateway Package

A robust API gateway with improved error handling, logging, security,
monitoring, and resilience patterns.

Features:
- Service routing and load balancing
- Circuit breaker pattern for resilience
- Rate limiting and security middleware
- Request/response logging and metrics
- Health checks and monitoring
- Authentication and authorization
- Configuration management

@Author: Enhanced by AI Assistant
@Original Author: xiangjh
"""

from .main import router
from .config import gateway_config, GatewayConfig
from .routing import GatewayRouter
from .auth import gateway_authenticator, authenticate_gateway_request, authorize_gateway_request
from .exceptions import (
    GatewayException,
    ServiceNotFoundError,
    ServiceUnavailableError,
    RequestTimeoutError,
    CircuitBreakerOpenError,
    RateLimitExceededError,
    AuthenticationError,
    AuthorizationError,
    RequestValidationError
)
from .models import (
    HealthCheckResponse,
    ServiceHealthResponse,
    MetricsResponse,
    ErrorResponse,
    GatewayConfigResponse,
    GatewayStatusResponse
)
from .utils import CircuitBreaker, RequestMetrics, generate_request_id
from .middleware import (
    RequestLoggingMiddleware,
    RateLimitMiddleware,
    SecurityMiddleware
)

__version__ = "2.0.0"
__author__ = "Enhanced by AI Assistant"

__all__ = [
    # Main router
    "router",
    
    # Configuration
    "gateway_config",
    "GatewayConfig",
    
    # Core components
    "GatewayRouter",
    "gateway_authenticator",
    "authenticate_gateway_request",
    "authorize_gateway_request",
    
    # Exceptions
    "GatewayException",
    "ServiceNotFoundError",
    "ServiceUnavailableError",
    "RequestTimeoutError",
    "CircuitBreakerOpenError",
    "RateLimitExceededError",
    "AuthenticationError",
    "AuthorizationError",
    "RequestValidationError",
    
    # Models
    "HealthCheckResponse",
    "ServiceHealthResponse",
    "MetricsResponse",
    "ErrorResponse",
    "GatewayConfigResponse",
    "GatewayStatusResponse",
    
    # Utilities
    "CircuitBreaker",
    "RequestMetrics",
    "generate_request_id",
    
    # Middleware
    "RequestLoggingMiddleware",
    "RateLimitMiddleware",
    "SecurityMiddleware",
]
