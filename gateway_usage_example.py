#!/usr/bin/env python3
"""
Gateway模块使用示例
演示如何使用增强版Gateway模块的各种功能
"""

import asyncio
import httpx
import time
from datetime import datetime

# 示例1: 基本的Gateway使用
def example_basic_usage():
    """基本使用示例"""
    print("📝 示例1: 基本Gateway使用")
    print("="*40)
    
    # 导入Gateway组件
    from gateway.utils import CircuitBreaker, RequestMetrics
    from gateway.exceptions import ServiceNotFoundError
    
    # 创建熔断器
    circuit_breaker = CircuitBreaker(
        failure_threshold=3,
        timeout=60.0,
        success_threshold=2
    )
    
    print(f"✅ 熔断器创建: 失败阈值={circuit_breaker.failure_threshold}")
    print(f"   当前状态: {circuit_breaker.state.value}")
    
    # 创建指标收集器
    metrics = RequestMetrics(max_metrics=1000)
    
    # 模拟一些请求
    test_requests = [
        ("user-service", "GET", 200, 0.15, True),
        ("user-service", "POST", 201, 0.25, True),
        ("user-service", "GET", 500, 0.8, False),
        ("order-service", "GET", 200, 0.12, True),
        ("order-service", "PUT", 404, 0.3, False),
    ]
    
    for service, method, status, duration, success in test_requests:
        metrics.record_request(service, method, status, duration, success)
        
        # 根据成功/失败更新熔断器
        if success:
            circuit_breaker.record_success()
        else:
            circuit_breaker.record_failure()
    
    print(f"✅ 记录了 {len(test_requests)} 个请求")
    
    # 获取指标
    user_metrics = metrics.get_service_metrics("user-service")
    print(f"✅ user-service指标:")
    print(f"   - 总请求数: {user_metrics['total_requests']}")
    print(f"   - 成功率: {user_metrics['success_rate']:.2%}")
    print(f"   - 平均响应时间: {user_metrics['average_duration']:.3f}s")
    
    overall_metrics = metrics.get_overall_metrics()
    print(f"✅ 整体指标:")
    print(f"   - 总请求数: {overall_metrics['total_requests']}")
    print(f"   - 成功率: {overall_metrics['success_rate']:.2%}")
    
    print()


# 示例2: 熔断器状态演示
def example_circuit_breaker():
    """熔断器状态演示"""
    print("📝 示例2: 熔断器状态演示")
    print("="*40)
    
    from gateway.utils import CircuitBreaker
    
    # 创建一个敏感的熔断器（低阈值用于演示）
    cb = CircuitBreaker(failure_threshold=2, timeout=3.0)
    
    print(f"初始状态: {cb.state.value}")
    
    # 模拟第一次失败
    cb.record_failure()
    print(f"第1次失败后: {cb.state.value} (失败计数: {cb.stats.failure_count})")
    
    # 模拟第二次失败 - 应该打开熔断器
    cb.record_failure()
    print(f"第2次失败后: {cb.state.value} (失败计数: {cb.stats.failure_count})")
    
    if cb.is_open():
        print("🔴 熔断器已打开，阻止请求通过")
        
        # 等待一段时间
        print("⏳ 等待超时...")
        time.sleep(3.1)
        
        # 检查是否转为半开状态
        if not cb.is_open() and cb.is_half_open():
            print("🟡 熔断器转为半开状态，允许少量请求通过")
            
            # 模拟成功请求恢复
            cb.record_success()
            cb.record_success()  # 达到成功阈值
            
            if cb.state.value == "closed":
                print("🟢 熔断器已关闭，服务恢复正常")
    
    # 显示最终统计
    stats = cb.get_stats()
    print(f"✅ 最终统计:")
    print(f"   - 成功次数: {stats['success_count']}")
    print(f"   - 失败次数: {stats['failure_count']}")
    print(f"   - 当前状态: {stats['state']}")
    
    print()


# 示例3: 异常处理演示
def example_exception_handling():
    """异常处理演示"""
    print("📝 示例3: 异常处理演示")
    print("="*40)
    
    from gateway.exceptions import (
        ServiceNotFoundError, ServiceUnavailableError,
        CircuitBreakerOpenError, RateLimitExceededError
    )
    
    # 演示不同类型的异常
    exceptions_demo = [
        (ServiceNotFoundError, "请求的服务不存在", 404),
        (ServiceUnavailableError, "服务暂时不可用", 503),
        (CircuitBreakerOpenError, "熔断器已打开", 503),
        (RateLimitExceededError, "超出请求限制", 429),
    ]
    
    for exception_class, message, expected_code in exceptions_demo:
        try:
            raise exception_class(message)
        except exception_class as e:
            print(f"✅ {exception_class.__name__}:")
            print(f"   - 消息: {e.message}")
            print(f"   - 状态码: {e.status_code} (期望: {expected_code})")
            print(f"   - 详情: {e.details}")
    
    print()


# 示例4: 配置模型验证
def example_config_validation():
    """配置模型验证演示"""
    print("📝 示例4: 配置模型验证")
    print("="*40)
    
    from gateway.models import ServiceConfigModel, RateLimitConfigModel
    
    # 创建有效的服务配置
    try:
        service_config = ServiceConfigModel(
            name="example-service",
            base_url="http://example-service:8080",
            timeout=30.0,
            max_retries=3,
            circuit_breaker_threshold=5,
            enabled=True
        )
        print(f"✅ 有效服务配置:")
        print(f"   - 服务名: {service_config.name}")
        print(f"   - 基础URL: {service_config.base_url}")
        print(f"   - 超时: {service_config.timeout}s")
        print(f"   - 最大重试: {service_config.max_retries}")
    except Exception as e:
        print(f"❌ 服务配置创建失败: {e}")
    
    # 演示配置验证
    print("\n🔍 测试配置验证:")
    
    # 测试无效超时
    try:
        ServiceConfigModel(
            name="invalid-service",
            base_url="http://invalid:8080",
            timeout=-1.0  # 无效值
        )
        print("❌ 应该抛出验证错误")
    except ValueError as e:
        print(f"✅ 超时验证正常: {e}")
    
    # 创建限流配置
    try:
        rate_limit_config = RateLimitConfigModel(
            enabled=True,
            requests_per_minute=100,
            burst_size=20,
            key_func="ip"
        )
        print(f"✅ 限流配置:")
        print(f"   - 启用: {rate_limit_config.enabled}")
        print(f"   - 每分钟请求数: {rate_limit_config.requests_per_minute}")
        print(f"   - 突发大小: {rate_limit_config.burst_size}")
        print(f"   - 键函数: {rate_limit_config.key_func}")
    except Exception as e:
        print(f"❌ 限流配置创建失败: {e}")
    
    print()


# 示例5: 工具函数使用
def example_utility_functions():
    """工具函数使用演示"""
    print("📝 示例5: 工具函数使用")
    print("="*40)
    
    from gateway.utils import generate_request_id, sanitize_headers, format_duration
    
    # 生成请求ID
    request_ids = [generate_request_id() for _ in range(3)]
    print("✅ 生成的请求ID:")
    for i, req_id in enumerate(request_ids, 1):
        print(f"   {i}. {req_id}")
    
    # 头部信息脱敏
    original_headers = {
        "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "cookie": "session=abc123; user=john; token=secret",
        "x-api-key": "sk-1234567890abcdef",
        "content-type": "application/json",
        "user-agent": "MyApp/1.0",
        "accept": "application/json"
    }
    
    sanitized = sanitize_headers(original_headers)
    print("\n✅ 头部信息脱敏:")
    for key, value in sanitized.items():
        original = original_headers[key]
        if value == "***REDACTED***":
            print(f"   {key}: {original} -> {value}")
        else:
            print(f"   {key}: {value}")
    
    # 时间格式化
    durations = [0.001, 0.5, 1.5, 30.0, 90.5, 3661.0]
    print("\n✅ 时间格式化:")
    for duration in durations:
        formatted = format_duration(duration)
        print(f"   {duration:8.3f}s -> {formatted}")
    
    print()


async def example_http_requests():
    """HTTP请求示例（需要运行的服务器）"""
    print("📝 示例6: HTTP请求演示")
    print("="*40)
    
    base_url = "http://localhost:8000/api"
    
    # 示例请求（需要实际运行的服务器）
    example_requests = [
        ("GET", f"{base_url}/gateway/health", "健康检查"),
        ("GET", f"{base_url}/gateway/metrics", "获取指标"),
        ("GET", f"{base_url}/gateway/services/cluster/health", "服务健康检查"),
    ]
    
    print("🔍 示例HTTP请求（需要运行的服务器）:")
    for method, url, description in example_requests:
        print(f"   {method} {url}")
        print(f"   描述: {description}")
    
    print("\n💡 使用curl测试:")
    print('   curl -H "Authorization: Bearer your-token" http://localhost:8000/api/gateway/health')
    print('   curl -H "Authorization: Bearer your-token" http://localhost:8000/api/gateway/metrics')
    
    print()


def main():
    """运行所有示例"""
    print("🚀 Gateway模块使用示例")
    print("=" * 60)
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    print()
    
    # 运行所有示例
    examples = [
        example_basic_usage,
        example_circuit_breaker,
        example_exception_handling,
        example_config_validation,
        example_utility_functions,
        lambda: asyncio.run(example_http_requests()),
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            example_func()
        except Exception as e:
            print(f"❌ 示例 {i} 执行失败: {e}")
            import traceback
            traceback.print_exc()
            print()
    
    print("🎉 所有示例演示完成!")
    print("\n📚 更多信息请参考:")
    print("   - docs/gateway/README.md")
    print("   - docs/gateway/ENHANCEMENTS.md")
    print("   - gateway_test_report.md")


if __name__ == "__main__":
    main()
