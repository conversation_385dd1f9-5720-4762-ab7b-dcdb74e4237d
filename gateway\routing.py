"""
Gateway Routing Module

Handles service discovery, request routing, and load balancing.

@Author: Enhanced by AI Assistant
"""

import json
import time
from typing import Dict, Optional, Any
from urllib.parse import urljoin

import httpx
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse, Response

from core.config.app_logger import logger
from .config import gateway_config, ServiceConfig
from .exceptions import (
    ServiceNotFoundError, 
    ServiceUnavailableError, 
    RequestTimeoutError,
    CircuitBreakerOpenError
)
from .utils import CircuitBreaker, RequestMetrics


class GatewayRouter:
    """Main gateway router class."""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.metrics = RequestMetrics()
        self._init_circuit_breakers()
    
    def _init_circuit_breakers(self):
        """Initialize circuit breakers for all services."""
        for service_name, service_config in gateway_config.services.items():
            self.circuit_breakers[service_name] = CircuitBreaker(
                failure_threshold=service_config.circuit_breaker_threshold,
                timeout=service_config.circuit_breaker_timeout
            )
    
    async def route_request(self, request: Request, server: str, path: str) -> Response:
        """
        Route request to the appropriate service.
        
        Args:
            request: The incoming HTTP request
            server: The target service identifier
            path: The path to forward to the target service
            
        Returns:
            Response from the target service
            
        Raises:
            ServiceNotFoundError: If the service is not configured
            ServiceUnavailableError: If the service is down
            CircuitBreakerOpenError: If the circuit breaker is open
        """
        start_time = time.time()
        
        try:
            # Get service configuration
            service_config = self._get_service_config(server)
            
            # Check circuit breaker
            circuit_breaker = self.circuit_breakers.get(server)
            if circuit_breaker and circuit_breaker.is_open():
                raise CircuitBreakerOpenError(f"Circuit breaker open for service: {server}")
            
            # Forward the request
            response = await self._forward_request(request, service_config, path)
            
            # Record success metrics
            self.metrics.record_request(
                service=server,
                method=request.method,
                status_code=response.status_code,
                duration=time.time() - start_time,
                success=True
            )
            
            # Reset circuit breaker on success
            if circuit_breaker:
                circuit_breaker.record_success()
            
            return response
            
        except Exception as e:
            # Record failure metrics
            self.metrics.record_request(
                service=server,
                method=request.method,
                status_code=getattr(e, 'status_code', 500),
                duration=time.time() - start_time,
                success=False
            )
            
            # Record failure in circuit breaker
            circuit_breaker = self.circuit_breakers.get(server)
            if circuit_breaker:
                circuit_breaker.record_failure()
            
            # Log the error
            logger.error(
                f"Gateway routing error for {server}/{path}: {str(e)}",
                extra={
                    "service": server,
                    "path": path,
                    "method": request.method,
                    "error_type": type(e).__name__,
                    "duration": time.time() - start_time
                }
            )
            
            raise
    
    def _get_service_config(self, server: str) -> ServiceConfig:
        """Get configuration for a service."""
        service_config = gateway_config.services.get(server)
        if not service_config:
            raise ServiceNotFoundError(f"Service not found: {server}")
        
        if not service_config.enabled:
            raise ServiceUnavailableError(f"Service disabled: {server}")
        
        return service_config
    
    async def _forward_request(
        self, 
        request: Request, 
        service_config: ServiceConfig, 
        path: str
    ) -> Response:
        """Forward request to the target service."""
        
        # Prepare headers
        headers = self._prepare_headers(request)
        
        # Prepare URL
        target_url = urljoin(service_config.base_url, path)
        
        # Read request body
        body = await request.body()
        
        # Configure HTTP client
        timeout = httpx.Timeout(service_config.timeout)
        
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.request(
                    method=request.method,
                    url=target_url,
                    headers=headers,
                    content=body,
                    params=request.query_params,
                    follow_redirects=False
                )
                
                return self._create_response(response)
                
        except httpx.TimeoutException as e:
            raise RequestTimeoutError(f"Request timeout for service: {service_config.name}") from e
        except httpx.RequestError as e:
            raise ServiceUnavailableError(f"Service unavailable: {service_config.name}") from e
    
    def _prepare_headers(self, request: Request) -> Dict[str, str]:
        """Prepare headers for forwarding."""
        headers = dict(request.headers)
        
        # Remove problematic headers
        headers.pop("host", None)
        headers.pop("content-length", None)
        
        # Add user information if available
        if hasattr(request.state, "user") and request.state.user:
            user = request.state.user
            headers["X-User-Id"] = str(user.get("userId", ""))
            headers["X-User-Name"] = str(user.get("username", ""))
            if "permissions" in user:
                headers["X-User-Permissions"] = json.dumps(list(user["permissions"]))
        
        # Add gateway headers
        headers["X-Gateway-Forwarded"] = "true"
        headers["X-Gateway-Timestamp"] = str(int(time.time()))
        
        return headers
    
    def _create_response(self, upstream_response: httpx.Response) -> Response:
        """Create FastAPI response from upstream response."""
        
        # Handle different content types
        content_type = upstream_response.headers.get("content-type", "")
        
        if "application/json" in content_type:
            try:
                content = upstream_response.json()
                return JSONResponse(
                    content=content,
                    status_code=upstream_response.status_code,
                    headers=dict(upstream_response.headers)
                )
            except json.JSONDecodeError:
                # Fallback to text response if JSON parsing fails
                pass
        
        # Return raw response for non-JSON content
        return Response(
            content=upstream_response.content,
            status_code=upstream_response.status_code,
            headers=dict(upstream_response.headers),
            media_type=content_type
        )
    
    def get_service_health(self, service_name: str) -> Dict[str, Any]:
        """Get health status of a service."""
        service_config = gateway_config.services.get(service_name)
        if not service_config:
            return {"status": "not_found", "service": service_name}
        
        circuit_breaker = self.circuit_breakers.get(service_name)
        circuit_breaker_status = "closed"
        if circuit_breaker:
            if circuit_breaker.is_open():
                circuit_breaker_status = "open"
            elif circuit_breaker.is_half_open():
                circuit_breaker_status = "half_open"
        
        return {
            "service": service_name,
            "status": "enabled" if service_config.enabled else "disabled",
            "base_url": service_config.base_url,
            "circuit_breaker": circuit_breaker_status,
            "metrics": self.metrics.get_service_metrics(service_name)
        }
