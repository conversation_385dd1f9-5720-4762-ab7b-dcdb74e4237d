"""
Gateway Integration Tests

Tests the complete gateway functionality with real components.

@Author: Enhanced by AI Assistant
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from fastapi import FastAPI
from fastapi.testclient import TestClient
import httpx

from gateway.main import router
from gateway.config import gateway_config
from gateway.routing import GatewayRouter
from gateway.utils import CircuitBreaker, RequestMetrics


class TestGatewayIntegration:
    """Integration tests for the complete gateway system."""
    
    def setup_method(self):
        """Setup test environment."""
        self.app = FastAPI()
        self.app.include_router(router)
        self.client = TestClient(self.app)
        
        # Reset gateway state
        self.gateway_router = GatewayRouter()
    
    def test_gateway_health_endpoint_integration(self):
        """Test health endpoint with real gateway components."""
        with patch('gateway.main.gateway_router', self.gateway_router):
            response = self.client.get("/gateway/health")
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response structure
            assert "status" in data
            assert "timestamp" in data
            assert "services" in data
            assert "version" in data
            assert "uptime" in data
            
            # Verify health status
            assert data["status"] == "healthy"
            assert data["version"] == "2.0.0"
            assert isinstance(data["uptime"], (int, float))
    
    def test_gateway_metrics_endpoint_integration(self):
        """Test metrics endpoint with real metrics collection."""
        with patch('gateway.main.gateway_router', self.gateway_router):
            # Record some test metrics
            self.gateway_router.metrics.record_request(
                service="test-service",
                method="GET",
                status_code=200,
                duration=0.1,
                success=True
            )
            self.gateway_router.metrics.record_request(
                service="test-service",
                method="POST",
                status_code=500,
                duration=0.5,
                success=False
            )
            
            response = self.client.get("/gateway/metrics")
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify metrics structure
            assert "total_requests" in data
            assert "success_rate" in data
            assert "error_rate" in data
            assert "average_duration" in data
            assert "services" in data
            
            # Verify calculated metrics
            assert data["total_requests"] == 2
            assert data["success_rate"] == 0.5  # 1 success out of 2
            assert data["error_rate"] == 0.5    # 1 failure out of 2
            assert data["average_duration"] == 0.3  # (0.1 + 0.5) / 2
    
    def test_service_health_endpoint_integration(self):
        """Test service health endpoint with real service configuration."""
        with patch('gateway.main.gateway_router', self.gateway_router):
            # Test with configured service
            response = self.client.get("/gateway/services/cluster/health")
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify service health structure
            assert "service" in data
            assert "status" in data
            assert "circuit_breaker" in data
            assert "metrics" in data
            
            # Test with non-existent service
            response = self.client.get("/gateway/services/nonexistent/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "not_found"
            assert data["service"] == "nonexistent"


class TestCircuitBreakerIntegration:
    """Test circuit breaker integration with gateway routing."""
    
    def setup_method(self):
        """Setup test environment."""
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=2,
            timeout=1.0,
            success_threshold=1
        )
    
    def test_circuit_breaker_state_transitions(self):
        """Test circuit breaker state transitions."""
        # Initial state should be closed
        assert not self.circuit_breaker.is_open()
        assert not self.circuit_breaker.is_half_open()
        
        # Record failures to open circuit breaker
        self.circuit_breaker.record_failure()
        assert not self.circuit_breaker.is_open()  # Still closed
        
        self.circuit_breaker.record_failure()
        assert self.circuit_breaker.is_open()  # Now open
        
        # Wait for timeout and check half-open transition
        time.sleep(1.1)  # Wait longer than timeout
        assert not self.circuit_breaker.is_open()  # Should transition to half-open
        assert self.circuit_breaker.is_half_open()
        
        # Record success to close circuit breaker
        self.circuit_breaker.record_success()
        assert not self.circuit_breaker.is_open()
        assert not self.circuit_breaker.is_half_open()
    
    def test_circuit_breaker_statistics(self):
        """Test circuit breaker statistics collection."""
        # Record some operations
        self.circuit_breaker.record_success()
        self.circuit_breaker.record_failure()
        self.circuit_breaker.record_success()
        
        stats = self.circuit_breaker.get_stats()
        
        assert stats["success_count"] == 2
        assert stats["failure_count"] == 1
        assert stats["state"] == "closed"
        assert "last_success_time" in stats
        assert "last_failure_time" in stats


class TestRequestMetricsIntegration:
    """Test request metrics integration."""
    
    def setup_method(self):
        """Setup test environment."""
        self.metrics = RequestMetrics(max_metrics=100)
    
    def test_metrics_collection_and_aggregation(self):
        """Test metrics collection and aggregation."""
        # Record requests for multiple services
        services = ["service-a", "service-b", "service-c"]
        methods = ["GET", "POST", "PUT"]
        status_codes = [200, 201, 400, 500]
        
        for i in range(20):
            service = services[i % len(services)]
            method = methods[i % len(methods)]
            status_code = status_codes[i % len(status_codes)]
            success = status_code < 400
            
            self.metrics.record_request(
                service=service,
                method=method,
                status_code=status_code,
                duration=0.1 + (i * 0.01),  # Varying duration
                success=success
            )
        
        # Test overall metrics
        overall = self.metrics.get_overall_metrics()
        assert overall["total_requests"] == 20
        assert 0 <= overall["success_rate"] <= 1
        assert 0 <= overall["error_rate"] <= 1
        assert overall["success_rate"] + overall["error_rate"] == 1.0
        
        # Test service-specific metrics
        for service in services:
            service_metrics = self.metrics.get_service_metrics(service)
            assert service_metrics["total_requests"] > 0
            assert 0 <= service_metrics["success_rate"] <= 1
    
    def test_metrics_time_window_filtering(self):
        """Test metrics filtering by time window."""
        # Record old metrics
        with patch('time.time', return_value=1000.0):
            self.metrics.record_request("test", "GET", 200, 0.1, True)
        
        # Record recent metrics
        with patch('time.time', return_value=1500.0):
            self.metrics.record_request("test", "POST", 201, 0.2, True)
            
            # Get metrics with 300 second window
            metrics = self.metrics.get_service_metrics("test", time_window=300)
            
            # Should only include recent request
            assert metrics["total_requests"] == 1
            assert 201 in metrics["status_codes"]
            assert 200 not in metrics["status_codes"]


class TestGatewayConfigurationIntegration:
    """Test gateway configuration integration."""
    
    def test_gateway_config_loading(self):
        """Test that gateway configuration loads correctly."""
        # Test that configuration is loaded
        assert hasattr(gateway_config, 'services')
        assert hasattr(gateway_config, 'rate_limit')
        assert hasattr(gateway_config, 'security')
        assert hasattr(gateway_config, 'monitoring')
        
        # Test default values
        assert gateway_config.default_timeout > 0
        assert gateway_config.max_concurrent_requests > 0
        
        # Test service configuration
        if "cluster" in gateway_config.services:
            cluster_config = gateway_config.services["cluster"]
            assert cluster_config.name == "cluster"
            assert cluster_config.timeout > 0
            assert cluster_config.max_retries >= 0
    
    def test_rate_limit_configuration(self):
        """Test rate limiting configuration."""
        rate_limit = gateway_config.rate_limit
        
        assert isinstance(rate_limit.enabled, bool)
        assert rate_limit.requests_per_minute > 0
        assert rate_limit.burst_size > 0
        assert rate_limit.key_func in ["ip", "user", "custom"]
    
    def test_security_configuration(self):
        """Test security configuration."""
        security = gateway_config.security
        
        assert isinstance(security.cors_enabled, bool)
        assert isinstance(security.cors_origins, list)
        assert isinstance(security.cors_methods, list)
        assert isinstance(security.cors_headers, list)
        assert security.max_request_size > 0
        assert isinstance(security.require_auth, bool)
        assert isinstance(security.blocked_ips, list)
    
    def test_monitoring_configuration(self):
        """Test monitoring configuration."""
        monitoring = gateway_config.monitoring
        
        assert isinstance(monitoring.metrics_enabled, bool)
        assert isinstance(monitoring.tracing_enabled, bool)
        assert isinstance(monitoring.health_check_enabled, bool)
        assert isinstance(monitoring.log_requests, bool)
        assert isinstance(monitoring.log_responses, bool)
        assert isinstance(monitoring.log_headers, bool)
        assert isinstance(monitoring.performance_monitoring, bool)


@pytest.mark.asyncio
class TestAsyncGatewayIntegration:
    """Async integration tests for gateway functionality."""
    
    async def test_async_request_processing(self):
        """Test async request processing flow."""
        # This would test the complete async flow
        # from request reception to response delivery
        pass
    
    async def test_concurrent_request_handling(self):
        """Test handling of concurrent requests."""
        # This would test that the gateway can handle
        # multiple concurrent requests efficiently
        pass
    
    async def test_timeout_handling(self):
        """Test request timeout handling."""
        # This would test that timeouts are properly
        # handled and don't block other requests
        pass
