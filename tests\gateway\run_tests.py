"""
Gateway Test Runner

Script to run all gateway tests with proper configuration.

@Author: Enhanced by AI Assistant
"""

import sys
import os
import pytest
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def run_gateway_tests():
    """Run all gateway tests."""
    
    # Test directory
    test_dir = Path(__file__).parent
    
    # Configure pytest arguments
    pytest_args = [
        str(test_dir),
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--strict-markers",  # Strict marker checking
        "-x",  # Stop on first failure
        "--disable-warnings",  # Disable warnings for cleaner output
    ]
    
    # Add coverage if available
    try:
        import pytest_cov
        pytest_args.extend([
            "--cov=gateway",
            "--cov-report=term-missing",
            "--cov-report=html:tests/gateway/htmlcov"
        ])
        print("Running tests with coverage...")
    except ImportError:
        print("Running tests without coverage (install pytest-cov for coverage reports)...")
    
    # Run tests
    print(f"Running gateway tests from: {test_dir}")
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("\n✅ All gateway tests passed!")
    else:
        print(f"\n❌ Tests failed with exit code: {exit_code}")
    
    return exit_code


def run_specific_test(test_file: str, test_function: str = None):
    """Run a specific test file or function."""
    
    test_path = Path(__file__).parent / test_file
    if not test_path.exists():
        print(f"❌ Test file not found: {test_path}")
        return 1
    
    pytest_args = [str(test_path), "-v"]
    
    if test_function:
        pytest_args[0] += f"::{test_function}"
    
    print(f"Running specific test: {test_path}")
    if test_function:
        print(f"Function: {test_function}")
    
    return pytest.main(pytest_args)


if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Run specific test
        test_file = sys.argv[1]
        test_function = sys.argv[2] if len(sys.argv) > 2 else None
        exit_code = run_specific_test(test_file, test_function)
    else:
        # Run all tests
        exit_code = run_gateway_tests()
    
    sys.exit(exit_code)
