"""
Enhanced API Gateway Module

This module provides a robust API gateway with improved error handling,
logging, security, and monitoring capabilities.

@Author: Enhanced by AI Assistant
@Original Author: xiangjh
"""

import time
from fastapi import APIRouter, Depends, Request
from core.config.security import check_user
from core.config.app_logger import logger

from .routing import GatewayRouter
from .middleware import setup_gateway_middleware
from .exceptions import setup_exception_handlers

# Initialize the main router
router = APIRouter(
    prefix="/gateway",
    tags=["Gateway"],
    dependencies=[Depends(check_user)]
)

# Initialize gateway components
gateway_router = GatewayRouter()

# Setup middleware and exception handlers
setup_gateway_middleware(router)
setup_exception_handlers(router)

# Health check endpoint
@router.get("/health", response_model=dict, tags=["Health"])
async def health_check():
    """
    Gateway health check endpoint.

    Returns:
        Health status of the gateway and all configured services
    """

    # Get health status for all services
    services_health = {}
    for service_name in gateway_router.circuit_breakers:
        services_health[service_name] = gateway_router.get_service_health(service_name)

    # Calculate uptime (simplified - would need actual start time in production)
    uptime = time.time() - getattr(health_check, '_start_time', time.time())
    if not hasattr(health_check, '_start_time'):
        health_check._start_time = time.time()

    return {
        "status": "healthy",
        "timestamp": time.time(),
        "services": services_health,
        "version": "2.0.0",
        "uptime": uptime
    }


# Metrics endpoint
@router.get("/metrics", response_model=dict, tags=["Monitoring"])
async def get_metrics(time_window: int = 300):
    """
    Get gateway metrics.

    Args:
        time_window: Time window in seconds for metrics calculation

    Returns:
        Gateway metrics including request counts, success rates, and performance data
    """
    return gateway_router.metrics.get_overall_metrics(time_window)


# Service health endpoint
@router.get("/services/{service_name}/health", response_model=dict, tags=["Health"])
async def get_service_health(service_name: str):
    """
    Get health status for a specific service.

    Args:
        service_name: Name of the service to check

    Returns:
        Health status and metrics for the specified service
    """
    return gateway_router.get_service_health(service_name)


# Main gateway endpoint
@router.api_route(
    "/{server}/{path:path}",
    methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    include_in_schema=False,
)
async def gateway_endpoint(
    request: Request,
    server: str,
    path: str
):
    """
    Main gateway function to route requests to microservices.

    Args:
        request: The incoming HTTP request
        server: The target service identifier
        path: The path to forward to the target service

    Returns:
        Response from the target service
    """
    logger.info(
        f"Gateway request: {request.method} /{server}/{path} "
        f"from {request.client.host if request.client else 'unknown'}"
    )

    return await gateway_router.route_request(request, server, path)
