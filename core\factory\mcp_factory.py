from typing import List
from mcpclient.mcp_client import <PERSON>cp<PERSON><PERSON>
from core.config.app_logger import logger
from core.config.app_config import config

# Mcp工厂类类，用于创建并初始化所有启用的 MCP 客户端
# @Author: xiangjh
class McpFactory:
    @classmethod
    async def create_mcp_clients(cls) -> List[McpClient]:
        servers = config.mcp_servers
        clients = []
        
        for server in servers:
            if not server.enabled:
                logger.info(f"MCPClient {server.key} 未启用")
                continue

            try:
                client = McpClient()
                if server.type == "stdio":
                    await client.connect_to_server(server.url)
                elif server.type == "sse":
                    await client.connect_to_server_url(server.url)
                
                clients.append(client)
                logger.info(f"MCPClient {server.key} 初始化成功")
            except Exception as e:
                logger.error(f"MCPClient {server.key} 初始化失败: {str(e)}", exc_info=True)
                continue

        return clients