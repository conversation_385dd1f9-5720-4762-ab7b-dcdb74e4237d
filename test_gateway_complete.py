#!/usr/bin/env python3
"""
完整的Gateway模块测试脚本
测试所有增强功能和组件
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试基础导入
        from gateway.utils import CircuitBreaker, RequestMetrics, generate_request_id
        print("✅ 工具类导入成功")
        
        from gateway.exceptions import (
            GatewayException, ServiceNotFoundError, ServiceUnavailableError,
            CircuitBreakerOpenError, RateLimitExceededError
        )
        print("✅ 异常类导入成功")
        
        from gateway.models import (
            HealthCheckResponse, ServiceConfigModel, 
            RateLimitConfigModel, MetricsResponse
        )
        print("✅ 数据模型导入成功")
        
        # 测试配置导入（可能需要配置文件）
        try:
            from gateway.config import GatewayConfig, ServiceConfig
            print("✅ 配置类导入成功")
        except Exception as e:
            print(f"⚠️ 配置类导入失败（可能需要配置文件）: {e}")
        
        # 测试路由导入
        try:
            from gateway.routing import GatewayRouter
            print("✅ 路由类导入成功")
        except Exception as e:
            print(f"⚠️ 路由类导入失败: {e}")
        
        # 测试主路由导入
        try:
            from gateway.main import router
            print("✅ 主路由导入成功")
            print(f"   - 路由前缀: {router.prefix}")
            print(f"   - 路由标签: {router.tags}")
        except Exception as e:
            print(f"⚠️ 主路由导入失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_circuit_breaker():
    """测试熔断器功能"""
    print("\n🔍 测试熔断器功能...")
    
    try:
        from gateway.utils import CircuitBreaker, CircuitBreakerState
        
        # 创建熔断器
        cb = CircuitBreaker(failure_threshold=3, timeout=2.0, success_threshold=2)
        
        # 测试初始状态
        assert cb.state == CircuitBreakerState.CLOSED
        assert not cb.is_open()
        assert not cb.is_half_open()
        print("✅ 初始状态: CLOSED")
        
        # 测试成功记录
        cb.record_success()
        assert cb.stats.success_count == 1
        print("✅ 成功记录功能正常")
        
        # 测试失败记录 - 达到阈值前
        cb.record_failure()
        cb.record_failure()
        assert cb.state == CircuitBreakerState.CLOSED
        print("✅ 失败记录功能正常，未达到阈值")
        
        # 测试失败记录 - 达到阈值
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        assert cb.is_open()
        print("✅ 熔断器正确打开")
        
        # 测试统计信息
        stats = cb.get_stats()
        assert stats["failure_count"] == 3
        assert stats["success_count"] == 1
        assert stats["state"] == "open"
        print("✅ 统计信息正确")
        
        # 测试超时后转为半开状态
        print("   等待超时转换...")
        time.sleep(2.1)  # 等待超过timeout时间
        
        # 检查是否转为半开状态
        is_open_after_timeout = cb.is_open()
        if not is_open_after_timeout:
            assert cb.is_half_open()
            print("✅ 超时后正确转为半开状态")
            
            # 测试半开状态下的成功恢复
            cb.record_success()
            cb.record_success()  # 达到success_threshold
            assert cb.state == CircuitBreakerState.CLOSED
            print("✅ 半开状态下成功恢复到关闭状态")
        
        print("🎉 熔断器测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 熔断器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_request_metrics():
    """测试请求指标收集"""
    print("\n🔍 测试请求指标收集...")
    
    try:
        from gateway.utils import RequestMetrics
        
        # 创建指标收集器
        metrics = RequestMetrics(max_metrics=100)
        
        # 记录一些请求
        test_data = [
            ("service-a", "GET", 200, 0.1, True),
            ("service-a", "POST", 201, 0.2, True),
            ("service-a", "GET", 500, 0.3, False),
            ("service-a", "PUT", 404, 0.15, False),
            ("service-b", "GET", 200, 0.05, True),
            ("service-b", "POST", 500, 0.4, False),
        ]
        
        for service, method, status, duration, success in test_data:
            metrics.record_request(service, method, status, duration, success)
        
        print(f"✅ 记录了 {len(test_data)} 个请求")
        
        # 测试服务A的指标
        service_a_metrics = metrics.get_service_metrics("service-a")
        assert service_a_metrics["total_requests"] == 4
        assert service_a_metrics["success_rate"] == 0.5  # 2成功/4总数
        assert service_a_metrics["error_rate"] == 0.5    # 2失败/4总数
        expected_avg = (0.1 + 0.2 + 0.3 + 0.15) / 4
        assert abs(service_a_metrics["average_duration"] - expected_avg) < 0.001
        print("✅ 服务A指标计算正确")
        
        # 测试服务B的指标
        service_b_metrics = metrics.get_service_metrics("service-b")
        assert service_b_metrics["total_requests"] == 2
        assert service_b_metrics["success_rate"] == 0.5  # 1成功/2总数
        print("✅ 服务B指标计算正确")
        
        # 测试整体指标
        overall_metrics = metrics.get_overall_metrics()
        assert overall_metrics["total_requests"] == 6
        assert overall_metrics["success_rate"] == 3/6  # 3成功/6总数
        assert overall_metrics["average_duration"] == sum([d[3] for d in test_data]) / 6
        print("✅ 整体指标计算正确")
        
        # 测试状态码分布
        service_a_status_codes = service_a_metrics["status_codes"]
        assert service_a_status_codes[200] == 1
        assert service_a_status_codes[201] == 1
        assert service_a_status_codes[500] == 1
        assert service_a_status_codes[404] == 1
        print("✅ 状态码分布统计正确")
        
        # 测试时间窗口过滤
        time_window_metrics = metrics.get_service_metrics("service-a", time_window=1)
        assert time_window_metrics["total_requests"] == 4  # 所有请求都在时间窗口内
        print("✅ 时间窗口过滤功能正常")
        
        print("🎉 请求指标测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 请求指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_utility_functions():
    """测试工具函数"""
    print("\n🔍 测试工具函数...")
    
    try:
        from gateway.utils import generate_request_id, sanitize_headers, format_duration
        
        # 测试请求ID生成
        id1 = generate_request_id()
        id2 = generate_request_id()
        assert id1 != id2
        assert len(id1) == 36  # UUID格式
        assert "-" in id1
        print("✅ 请求ID生成功能正常")
        
        # 测试头部信息脱敏
        headers = {
            "authorization": "Bearer secret-token-123",
            "cookie": "session=abc123; user=john",
            "x-api-key": "api-key-secret",
            "x-auth-token": "auth-token-secret",
            "content-type": "application/json",
            "user-agent": "test-client/1.0",
            "accept": "application/json"
        }
        
        sanitized = sanitize_headers(headers)
        
        # 敏感头部应该被脱敏
        assert sanitized["authorization"] == "***REDACTED***"
        assert sanitized["cookie"] == "***REDACTED***"
        assert sanitized["x-api-key"] == "***REDACTED***"
        assert sanitized["x-auth-token"] == "***REDACTED***"
        
        # 非敏感头部应该保持原样
        assert sanitized["content-type"] == "application/json"
        assert sanitized["user-agent"] == "test-client/1.0"
        assert sanitized["accept"] == "application/json"
        print("✅ 头部信息脱敏功能正常")
        
        # 测试持续时间格式化
        test_cases = [
            (0.001, "1.00ms"),
            (0.5, "500.00ms"),
            (0.999, "999.00ms"),
            (1.0, "1.00s"),
            (1.5, "1.50s"),
            (30.25, "30.25s"),
            (60.0, "1m 0.00s"),
            (90.5, "1m 30.50s"),
            (125.75, "2m 5.75s"),
            (3661.5, "61m 1.50s")
        ]
        
        for duration, expected in test_cases:
            result = format_duration(duration)
            assert result == expected, f"Expected {expected}, got {result} for {duration}"
        
        print("✅ 持续时间格式化功能正常")
        
        print("🎉 工具函数测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_models():
    """测试Pydantic模型"""
    print("\n🔍 测试Pydantic模型...")
    
    try:
        from gateway.models import (
            ServiceConfigModel, RateLimitConfigModel, 
            SecurityConfigModel, MonitoringConfigModel
        )
        
        # 测试服务配置模型
        service_config = ServiceConfigModel(
            name="test-service",
            base_url="http://test-service:8080",
            timeout=30.0,
            max_retries=3,
            circuit_breaker_threshold=5,
            circuit_breaker_timeout=60.0,
            enabled=True
        )
        
        assert service_config.name == "test-service"
        assert service_config.base_url == "http://test-service:8080"
        assert service_config.timeout == 30.0
        print("✅ 服务配置模型正常")
        
        # 测试验证功能 - 无效超时
        try:
            ServiceConfigModel(
                name="invalid-service",
                base_url="http://invalid:8080",
                timeout=-1.0  # 无效超时
            )
            assert False, "应该抛出验证错误"
        except ValueError as e:
            assert "positive" in str(e)
            print("✅ 超时验证功能正常")
        
        # 测试验证功能 - 无效重试次数
        try:
            ServiceConfigModel(
                name="invalid-service",
                base_url="http://invalid:8080",
                max_retries=-1  # 无效重试次数
            )
            assert False, "应该抛出验证错误"
        except ValueError as e:
            assert "negative" in str(e)
            print("✅ 重试次数验证功能正常")
        
        # 测试限流配置模型
        rate_limit_config = RateLimitConfigModel(
            enabled=True,
            requests_per_minute=100,
            burst_size=20,
            key_func="ip"
        )
        
        assert rate_limit_config.enabled is True
        assert rate_limit_config.requests_per_minute == 100
        assert rate_limit_config.key_func == "ip"
        print("✅ 限流配置模型正常")
        
        # 测试限流配置验证
        try:
            RateLimitConfigModel(
                requests_per_minute=0  # 无效值
            )
            assert False, "应该抛出验证错误"
        except ValueError as e:
            assert "positive" in str(e)
            print("✅ 限流配置验证功能正常")
        
        try:
            RateLimitConfigModel(
                key_func="invalid"  # 无效的key_func
            )
            assert False, "应该抛出验证错误"
        except ValueError as e:
            assert "ip, user, custom" in str(e)
            print("✅ key_func验证功能正常")
        
        # 测试安全配置模型
        security_config = SecurityConfigModel(
            cors_enabled=True,
            cors_origins=["http://localhost:3000", "https://example.com"],
            max_request_size=10485760,
            require_auth=True,
            blocked_ips=["*************"]
        )
        
        assert security_config.cors_enabled is True
        assert len(security_config.cors_origins) == 2
        assert security_config.max_request_size == 10485760
        print("✅ 安全配置模型正常")
        
        # 测试监控配置模型
        monitoring_config = MonitoringConfigModel(
            metrics_enabled=True,
            tracing_enabled=True,
            log_requests=True,
            log_responses=False,
            performance_monitoring=True
        )
        
        assert monitoring_config.metrics_enabled is True
        assert monitoring_config.log_responses is False
        print("✅ 监控配置模型正常")
        
        print("🎉 Pydantic模型测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ Pydantic模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_exceptions():
    """测试自定义异常"""
    print("\n🔍 测试自定义异常...")
    
    try:
        from gateway.exceptions import (
            GatewayException, ServiceNotFoundError, ServiceUnavailableError,
            RequestTimeoutError, CircuitBreakerOpenError, RateLimitExceededError,
            AuthenticationError, AuthorizationError, RequestValidationError
        )
        
        # 测试基础异常
        try:
            raise GatewayException("测试异常", status_code=500, details={"test": "data"})
        except GatewayException as e:
            assert e.status_code == 500
            assert e.message == "测试异常"
            assert e.details["test"] == "data"
            print("✅ 基础异常功能正常")
        
        # 测试服务未找到异常
        try:
            raise ServiceNotFoundError("服务未找到")
        except ServiceNotFoundError as e:
            assert e.status_code == 404
            assert "未找到" in e.message
            print("✅ 服务未找到异常正常")
        
        # 测试服务不可用异常
        try:
            raise ServiceUnavailableError("服务不可用")
        except ServiceUnavailableError as e:
            assert e.status_code == 503
            print("✅ 服务不可用异常正常")
        
        # 测试请求超时异常
        try:
            raise RequestTimeoutError("请求超时")
        except RequestTimeoutError as e:
            assert e.status_code == 504
            print("✅ 请求超时异常正常")
        
        # 测试熔断器打开异常
        try:
            raise CircuitBreakerOpenError("熔断器打开")
        except CircuitBreakerOpenError as e:
            assert e.status_code == 503
            print("✅ 熔断器打开异常正常")
        
        # 测试限流异常
        try:
            raise RateLimitExceededError("超出限流")
        except RateLimitExceededError as e:
            assert e.status_code == 429
            print("✅ 限流异常正常")
        
        # 测试认证异常
        try:
            raise AuthenticationError("认证失败")
        except AuthenticationError as e:
            assert e.status_code == 401
            print("✅ 认证异常正常")
        
        # 测试授权异常
        try:
            raise AuthorizationError("授权失败")
        except AuthorizationError as e:
            assert e.status_code == 403
            print("✅ 授权异常正常")
        
        # 测试请求验证异常
        try:
            raise RequestValidationError("请求验证失败")
        except RequestValidationError as e:
            assert e.status_code == 400
            print("✅ 请求验证异常正常")
        
        print("🎉 自定义异常测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 自定义异常测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始Gateway模块完整测试\n")
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("模块导入", test_imports),
        ("熔断器功能", test_circuit_breaker),
        ("请求指标收集", test_request_metrics),
        ("工具函数", test_utility_functions),
        ("Pydantic模型", test_models),
        ("自定义异常", test_exceptions),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print("="*50)
    print(f"总计: {len(test_results)} 个测试")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    
    if failed == 0:
        print("\n🎊 所有测试通过! Gateway模块功能正常!")
        return 0
    else:
        print(f"\n💥 有 {failed} 个测试失败!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
