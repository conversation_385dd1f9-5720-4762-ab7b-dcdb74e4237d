"""
Tests for Gateway Routing Module

@Author: Enhanced by AI Assistant
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
import httpx

from gateway.routing import GatewayRouter
from gateway.config import ServiceConfig
from gateway.exceptions import (
    ServiceNotFoundError,
    ServiceUnavailableError,
    RequestTimeoutError,
    CircuitBreakerOpenError
)


class TestGatewayRouter:
    """Test cases for GatewayRouter class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.router = GatewayRouter()
        
        # Mock service config
        self.test_service_config = ServiceConfig(
            name="test-service",
            base_url="http://test-service:8080",
            timeout=5.0,
            max_retries=2,
            circuit_breaker_threshold=3,
            circuit_breaker_timeout=30.0,
            enabled=True
        )
    
    def test_init_circuit_breakers(self):
        """Test circuit breaker initialization."""
        # Circuit breakers should be initialized for configured services
        assert len(self.router.circuit_breakers) >= 0
        
        # Each circuit breaker should be properly configured
        for service_name, cb in self.router.circuit_breakers.items():
            assert cb.failure_threshold > 0
            assert cb.timeout > 0
    
    def test_get_service_config_success(self):
        """Test successful service config retrieval."""
        # Mock gateway config
        with patch('gateway.routing.gateway_config') as mock_config:
            mock_config.services = {"test": self.test_service_config}
            
            config = self.router._get_service_config("test")
            assert config.name == "test-service"
            assert config.enabled is True
    
    def test_get_service_config_not_found(self):
        """Test service config retrieval for non-existent service."""
        with patch('gateway.routing.gateway_config') as mock_config:
            mock_config.services = {}
            
            with pytest.raises(ServiceNotFoundError):
                self.router._get_service_config("nonexistent")
    
    def test_get_service_config_disabled(self):
        """Test service config retrieval for disabled service."""
        disabled_config = ServiceConfig(
            name="disabled-service",
            base_url="http://disabled:8080",
            enabled=False
        )
        
        with patch('gateway.routing.gateway_config') as mock_config:
            mock_config.services = {"disabled": disabled_config}
            
            with pytest.raises(ServiceUnavailableError):
                self.router._get_service_config("disabled")
    
    def test_prepare_headers(self):
        """Test header preparation for forwarding."""
        # Create mock request
        mock_request = Mock(spec=Request)
        mock_request.headers = {
            "host": "gateway.example.com",
            "content-length": "100",
            "authorization": "Bearer token123",
            "user-agent": "test-client"
        }
        
        # Mock user state
        mock_request.state = Mock()
        mock_request.state.user = {
            "userId": "user123",
            "username": "testuser",
            "permissions": ["read", "write"]
        }
        
        headers = self.router._prepare_headers(mock_request)
        
        # Host and content-length should be removed
        assert "host" not in headers
        assert "content-length" not in headers
        
        # User headers should be added
        assert headers["X-User-Id"] == "user123"
        assert headers["X-User-Name"] == "testuser"
        assert "X-User-Permissions" in headers
        
        # Gateway headers should be added
        assert headers["X-Gateway-Forwarded"] == "true"
        assert "X-Gateway-Timestamp" in headers
        
        # Other headers should be preserved
        assert headers["authorization"] == "Bearer token123"
        assert headers["user-agent"] == "test-client"
    
    @pytest.mark.asyncio
    async def test_forward_request_success(self):
        """Test successful request forwarding."""
        # Mock request
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.headers = {"user-agent": "test"}
        mock_request.query_params = {"param": "value"}
        mock_request.body = AsyncMock(return_value=b"")
        
        # Mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "application/json"}
        mock_response.json.return_value = {"result": "success"}
        mock_response.content = b'{"result": "success"}'
        
        # Mock httpx client
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.request.return_value = mock_response
            
            response = await self.router._forward_request(
                mock_request, 
                self.test_service_config, 
                "test/path"
            )
            
            assert isinstance(response, JSONResponse)
            mock_client.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_forward_request_timeout(self):
        """Test request forwarding with timeout."""
        # Mock request
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.headers = {}
        mock_request.query_params = {}
        mock_request.body = AsyncMock(return_value=b"")
        
        # Mock httpx client to raise timeout
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.request.side_effect = httpx.TimeoutException("Timeout")
            
            with pytest.raises(RequestTimeoutError):
                await self.router._forward_request(
                    mock_request, 
                    self.test_service_config, 
                    "test/path"
                )
    
    @pytest.mark.asyncio
    async def test_route_request_circuit_breaker_open(self):
        """Test request routing with open circuit breaker."""
        # Mock request
        mock_request = Mock(spec=Request)
        mock_request.method = "GET"
        mock_request.client = Mock()
        mock_request.client.host = "127.0.0.1"
        
        # Open the circuit breaker
        circuit_breaker = self.router.circuit_breakers.get("test")
        if circuit_breaker:
            # Force circuit breaker to open state
            for _ in range(circuit_breaker.failure_threshold):
                circuit_breaker.record_failure()
        
        with patch('gateway.routing.gateway_config') as mock_config:
            mock_config.services = {"test": self.test_service_config}
            
            # Add circuit breaker for test service
            self.router.circuit_breakers["test"] = circuit_breaker
            
            if circuit_breaker and circuit_breaker.is_open():
                with pytest.raises(CircuitBreakerOpenError):
                    await self.router.route_request(mock_request, "test", "path")
    
    def test_get_service_health(self):
        """Test service health status retrieval."""
        with patch('gateway.routing.gateway_config') as mock_config:
            mock_config.services = {"test": self.test_service_config}
            
            health = self.router.get_service_health("test")
            
            assert health["service"] == "test"
            assert health["status"] in ["enabled", "disabled"]
            assert health["base_url"] == self.test_service_config.base_url
            assert "circuit_breaker" in health
            assert "metrics" in health
    
    def test_get_service_health_not_found(self):
        """Test service health for non-existent service."""
        health = self.router.get_service_health("nonexistent")
        
        assert health["status"] == "not_found"
        assert health["service"] == "nonexistent"
    
    def test_create_response_json(self):
        """Test response creation for JSON content."""
        # Mock upstream response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "application/json"}
        mock_response.json.return_value = {"data": "test"}
        
        response = self.router._create_response(mock_response)
        
        assert isinstance(response, JSONResponse)
        assert response.status_code == 200
    
    def test_create_response_non_json(self):
        """Test response creation for non-JSON content."""
        # Mock upstream response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"content-type": "text/plain"}
        mock_response.content = b"plain text response"
        mock_response.json.side_effect = ValueError("Not JSON")
        
        response = self.router._create_response(mock_response)

        assert response.status_code == 200
        # Note: FastAPI Response.body might not be directly accessible
        # In actual implementation, we'd check the response content differently
