"""
Gateway Models Module

Defines Pydantic models for request/response validation and API documentation.

@Author: Enhanced by AI Assistant
"""

from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from pydantic import BaseModel, Field, field_validator


class HealthCheckResponse(BaseModel):
    """Health check response model."""
    status: str = Field(..., description="Overall health status")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    services: Dict[str, Any] = Field(default_factory=dict, description="Service health details")
    version: str = Field(default="1.0.0", description="Gateway version")
    uptime: float = Field(..., description="Uptime in seconds")


class ServiceHealthResponse(BaseModel):
    """Individual service health response."""
    service: str = Field(..., description="Service name")
    status: str = Field(..., description="Service status")
    base_url: str = Field(..., description="Service base URL")
    circuit_breaker: str = Field(..., description="Circuit breaker status")
    metrics: Dict[str, Any] = Field(default_factory=dict, description="Service metrics")
    last_check: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class MetricsResponse(BaseModel):
    """Metrics response model."""
    total_requests: int = Field(..., description="Total number of requests")
    success_rate: float = Field(..., description="Success rate (0.0 to 1.0)")
    error_rate: float = Field(..., description="Error rate (0.0 to 1.0)")
    average_duration: float = Field(..., description="Average request duration in seconds")
    time_window: float = Field(..., description="Time window for metrics in seconds")
    services: Dict[str, Any] = Field(default_factory=dict, description="Per-service metrics")
    status_codes: Dict[int, int] = Field(default_factory=dict, description="Status code distribution")


class ErrorResponse(BaseModel):
    """Standard error response model."""
    error: 'ErrorDetail' = Field(..., description="Error details")


class ErrorDetail(BaseModel):
    """Error detail model."""
    code: int = Field(..., description="HTTP status code")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class GatewayConfigResponse(BaseModel):
    """Gateway configuration response model."""
    services: Dict[str, Any] = Field(..., description="Configured services")
    rate_limit: Dict[str, Any] = Field(..., description="Rate limiting configuration")
    security: Dict[str, Any] = Field(..., description="Security configuration")
    monitoring: Dict[str, Any] = Field(..., description="Monitoring configuration")


class ServiceConfigModel(BaseModel):
    """Service configuration model."""
    name: str = Field(..., description="Service name")
    base_url: str = Field(..., description="Service base URL")
    timeout: float = Field(30.0, description="Request timeout in seconds")
    max_retries: int = Field(3, description="Maximum number of retries")
    circuit_breaker_threshold: int = Field(5, description="Circuit breaker failure threshold")
    circuit_breaker_timeout: float = Field(60.0, description="Circuit breaker timeout in seconds")
    health_check_path: Optional[str] = Field("/health", description="Health check endpoint path")
    enabled: bool = Field(True, description="Whether the service is enabled")

    @field_validator('timeout')
    @classmethod
    def validate_timeout(cls, v):
        """Validate timeout value."""
        if v <= 0:
            raise ValueError('Timeout must be positive')
        return v

    @field_validator('max_retries')
    @classmethod
    def validate_max_retries(cls, v):
        """Validate max retries value."""
        if v < 0:
            raise ValueError('Max retries cannot be negative')
        return v


class RateLimitConfigModel(BaseModel):
    """Rate limiting configuration model."""
    enabled: bool = Field(True, description="Whether rate limiting is enabled")
    requests_per_minute: int = Field(100, description="Maximum requests per minute")
    burst_size: int = Field(20, description="Burst size for rate limiting")
    key_func: str = Field("ip", description="Rate limiting key function (ip, user, custom)")

    @field_validator('requests_per_minute')
    @classmethod
    def validate_requests_per_minute(cls, v):
        """Validate requests per minute value."""
        if v <= 0:
            raise ValueError('Requests per minute must be positive')
        return v

    @field_validator('key_func')
    @classmethod
    def validate_key_func(cls, v):
        """Validate key function value."""
        if v not in ['ip', 'user', 'custom']:
            raise ValueError('Key function must be one of: ip, user, custom')
        return v


class SecurityConfigModel(BaseModel):
    """Security configuration model."""
    cors_enabled: bool = Field(True, description="Whether CORS is enabled")
    cors_origins: List[str] = Field(["*"], description="Allowed CORS origins")
    cors_methods: List[str] = Field(
        ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"], 
        description="Allowed CORS methods"
    )
    cors_headers: List[str] = Field(["*"], description="Allowed CORS headers")
    max_request_size: int = Field(10 * 1024 * 1024, description="Maximum request size in bytes")
    require_auth: bool = Field(True, description="Whether authentication is required")
    allowed_user_agents: Optional[List[str]] = Field(None, description="Allowed user agents")
    blocked_ips: List[str] = Field([], description="Blocked IP addresses")


class MonitoringConfigModel(BaseModel):
    """Monitoring configuration model."""
    metrics_enabled: bool = Field(True, description="Whether metrics collection is enabled")
    tracing_enabled: bool = Field(True, description="Whether request tracing is enabled")
    health_check_enabled: bool = Field(True, description="Whether health checks are enabled")
    log_requests: bool = Field(True, description="Whether to log incoming requests")
    log_responses: bool = Field(False, description="Whether to log outgoing responses")
    log_headers: bool = Field(False, description="Whether to log request/response headers")
    performance_monitoring: bool = Field(True, description="Whether performance monitoring is enabled")


class CircuitBreakerStatsModel(BaseModel):
    """Circuit breaker statistics model."""
    state: str = Field(..., description="Circuit breaker state (closed, open, half_open)")
    failure_count: int = Field(..., description="Number of failures")
    success_count: int = Field(..., description="Number of successes")
    last_failure_time: Optional[float] = Field(None, description="Timestamp of last failure")
    last_success_time: Optional[float] = Field(None, description="Timestamp of last success")
    state_change_time: float = Field(..., description="Timestamp of last state change")
    failure_threshold: int = Field(..., description="Failure threshold for opening circuit")
    timeout: float = Field(..., description="Timeout before transitioning to half-open")


class RequestMetricModel(BaseModel):
    """Request metric model."""
    timestamp: float = Field(..., description="Request timestamp")
    service: str = Field(..., description="Target service")
    method: str = Field(..., description="HTTP method")
    status_code: int = Field(..., description="Response status code")
    duration: float = Field(..., description="Request duration in seconds")
    success: bool = Field(..., description="Whether the request was successful")
    request_id: str = Field(..., description="Unique request identifier")


class GatewayStatusResponse(BaseModel):
    """Gateway status response model."""
    status: str = Field(..., description="Gateway status")
    version: str = Field(..., description="Gateway version")
    uptime: float = Field(..., description="Uptime in seconds")
    total_requests: int = Field(..., description="Total requests processed")
    active_connections: int = Field(..., description="Number of active connections")
    memory_usage: Optional[Dict[str, Any]] = Field(None, description="Memory usage statistics")
    cpu_usage: Optional[float] = Field(None, description="CPU usage percentage")


# Update forward references
ErrorResponse.model_rebuild()
