#!/usr/bin/env python3
"""
Gateway集成测试 - 测试完整的gateway模块集成
"""

import sys
import asyncio
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gateway_config_loading():
    """测试gateway配置加载"""
    print("🔍 测试Gateway配置加载...")
    
    try:
        # 尝试加载配置
        from gateway.config import load_gateway_config, GatewayConfig
        
        # 创建测试配置
        config = load_gateway_config()
        
        assert isinstance(config, GatewayConfig)
        print("✅ 配置对象创建成功")
        
        # 检查配置属性
        assert hasattr(config, 'services')
        assert hasattr(config, 'rate_limit')
        assert hasattr(config, 'security')
        assert hasattr(config, 'monitoring')
        print("✅ 配置属性完整")
        
        # 检查服务配置
        if config.services:
            print(f"✅ 配置了 {len(config.services)} 个服务")
            for service_name, service_config in config.services.items():
                print(f"   - {service_name}: {service_config.base_url}")
        
        # 检查限流配置
        assert config.rate_limit.requests_per_minute > 0
        print(f"✅ 限流配置: {config.rate_limit.requests_per_minute} 请求/分钟")
        
        # 检查安全配置
        print(f"✅ 安全配置: 认证={config.security.require_auth}, CORS={config.security.cors_enabled}")
        
        # 检查监控配置
        print(f"✅ 监控配置: 指标={config.monitoring.metrics_enabled}, 日志={config.monitoring.log_requests}")
        
        print("🎉 Gateway配置加载测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ Gateway配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gateway_router():
    """测试Gateway路由器"""
    print("\n🔍 测试Gateway路由器...")
    
    try:
        from gateway.routing import GatewayRouter
        
        # 创建路由器
        router = GatewayRouter()
        
        # 检查初始化
        assert hasattr(router, 'circuit_breakers')
        assert hasattr(router, 'metrics')
        print("✅ 路由器初始化成功")
        
        # 测试服务健康检查
        health = router.get_service_health("nonexistent")
        assert health["status"] == "not_found"
        print("✅ 不存在服务的健康检查正常")
        
        # 如果有配置的服务，测试其健康状态
        if router.circuit_breakers:
            service_name = list(router.circuit_breakers.keys())[0]
            health = router.get_service_health(service_name)
            assert "service" in health
            assert "status" in health
            assert "circuit_breaker" in health
            print(f"✅ 服务 {service_name} 健康检查正常")
        
        print("🎉 Gateway路由器测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ Gateway路由器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gateway_main_router():
    """测试Gateway主路由"""
    print("\n🔍 测试Gateway主路由...")
    
    try:
        from gateway.main import router
        
        # 检查路由器配置
        assert router.prefix == "/gateway"
        assert "Gateway" in router.tags
        print("✅ 主路由配置正确")
        
        # 检查路由数量
        routes = router.routes
        print(f"✅ 配置了 {len(routes)} 个路由")
        
        # 检查关键路由
        route_paths = [route.path for route in routes if hasattr(route, 'path')]
        
        expected_paths = ["/health", "/metrics", "/services/{service_name}/health", "/{server}/{path:path}"]
        for expected_path in expected_paths:
            # 检查是否有匹配的路由（考虑前缀）
            full_path = router.prefix + expected_path
            print(f"   - 期望路由: {expected_path}")
        
        print("🎉 Gateway主路由测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ Gateway主路由测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_gateway_endpoints():
    """测试Gateway端点（模拟）"""
    print("\n🔍 测试Gateway端点...")
    
    try:
        from fastapi.testclient import TestClient
        from fastapi import FastAPI
        from gateway.main import router
        
        # 创建测试应用
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)
        
        # 模拟认证依赖
        with patch('gateway.main.check_user') as mock_auth:
            mock_user = Mock()
            mock_user.userId = "test-user"
            mock_auth.return_value = mock_user
            
            # 测试健康检查端点
            try:
                response = client.get("/gateway/health")
                print(f"   健康检查响应状态: {response.status_code}")
                if response.status_code == 200:
                    data = response.json()
                    assert "status" in data
                    print("✅ 健康检查端点正常")
                else:
                    print(f"⚠️ 健康检查返回状态码: {response.status_code}")
            except Exception as e:
                print(f"⚠️ 健康检查端点测试异常: {e}")
            
            # 测试指标端点
            try:
                response = client.get("/gateway/metrics")
                print(f"   指标端点响应状态: {response.status_code}")
                if response.status_code == 200:
                    data = response.json()
                    assert "total_requests" in data
                    print("✅ 指标端点正常")
                else:
                    print(f"⚠️ 指标端点返回状态码: {response.status_code}")
            except Exception as e:
                print(f"⚠️ 指标端点测试异常: {e}")
        
        print("🎉 Gateway端点测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ Gateway端点测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gateway_package_imports():
    """测试Gateway包导入"""
    print("\n🔍 测试Gateway包导入...")
    
    try:
        # 测试包级别导入
        import gateway
        
        # 检查主要导出
        assert hasattr(gateway, 'router')
        assert hasattr(gateway, 'gateway_config')
        assert hasattr(gateway, 'GatewayRouter')
        print("✅ 包级别导入成功")
        
        # 检查版本信息
        if hasattr(gateway, '__version__'):
            print(f"✅ Gateway版本: {gateway.__version__}")
        
        # 检查所有导出
        expected_exports = [
            'router', 'gateway_config', 'GatewayRouter',
            'CircuitBreaker', 'RequestMetrics',
            'ServiceNotFoundError', 'ServiceUnavailableError'
        ]
        
        available_exports = []
        for export in expected_exports:
            if hasattr(gateway, export):
                available_exports.append(export)
        
        print(f"✅ 可用导出: {len(available_exports)}/{len(expected_exports)}")
        for export in available_exports:
            print(f"   - {export}")
        
        print("🎉 Gateway包导入测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ Gateway包导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始Gateway集成测试\n")
    
    tests = [
        ("Gateway配置加载", test_gateway_config_loading),
        ("Gateway路由器", test_gateway_router),
        ("Gateway主路由", test_gateway_main_router),
        ("Gateway端点", lambda: asyncio.run(test_gateway_endpoints())),
        ("Gateway包导入", test_gateway_package_imports),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "="*50)
    print("📊 Gateway集成测试结果:")
    print("="*50)
    
    passed = sum(1 for _, result in results if result)
    failed = len(results) - passed
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} {status}")
    
    print("="*50)
    print(f"总计: {len(results)} | 通过: {passed} | 失败: {failed}")
    
    if failed == 0:
        print("\n🎊 所有集成测试通过!")
        print("✨ Gateway模块已准备就绪!")
        return 0
    else:
        print(f"\n💥 有 {failed} 个集成测试失败!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
