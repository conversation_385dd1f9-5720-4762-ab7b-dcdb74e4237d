from typing import Annotated,Dict, Any, Optional,AsyncIterator, <PERSON><PERSON>
from fastapi import  Request
from utils.common_utils import CommonUtils,stream_with_last_
from core.message.transmitter import Transmitter
from mcpclient.dify_client import DifyClient
import json
from core.config.app_logger import logger

################################
# 动态页面问答小助手智能体
################################
def process_chunk(chunk: Dict[str, Any]) -> Optional[str]:
    """
    处理 chunk 数据，根据类型返回对应的内容。
    
    支持：
        - type == 'stream': 返回 content 文本
        - type == 'tool_start': 返回 XML 格式的工具调用字符串
        - type == 'tool_result': {"type": "tool_result", "tool": "check_page_config", "output": "[TextContent(type='text', text='{\"success\": true, \"config_json\": {\"dynamicSql\": \"select did,name,type_name,code,dlevel,price,delete_flag from system_page_demo\", \"tableParams\": {\"fieldResizableTitle\": true}}, \"resultMessage\": \"配置验证通过，请继续调用 save_page_config 
                    工具进行保存\"}', annotations=None)]"}
        - type == 'error': 返回 message 文本
    """
    chunk_type = chunk.get("type")
    
    if chunk_type == "stream":
        return chunk.get("content", "")
    
    elif chunk_type == "tool_result":
        tool_name = chunk.get("tool")
        if not tool_name:
            return None
        
        tool_output = chunk.get("output", [])
        success = False
        flag = 1 # 默认成功
        if tool_output and isinstance(tool_output, list) and len(tool_output) > 0:
            text_content = tool_output[0]
            if hasattr(text_content, 'text') and text_content.text:
                try:
                    content_data = json.loads(text_content.text)
                    success = content_data.get('success', False)
                    flag = 1 if success else 0
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode TextContent: {e}")
        
        # 构建XML数据，包含success信息
        xml_data = f"""<message-embedded>
                            <widget>
                                <code>@BuildIn/Tool</code>
                                <props>
                                    <name>{tool_name}</name>
                                    <success>{flag}</success>
                                </props>
                            </widget>
                        </message-embedded>"""
        return xml_data
    elif chunk_type == "error":
        return chunk.get("message", "")
    else:
        # 其他类型暂不处理
        return None


async def process_and_filter_chunks(stream: AsyncIterator[Any]) -> AsyncIterator[str]:
    async for chunk in stream:
        if isinstance(chunk, str):
            chunk = json.loads(chunk)
        processed_data = process_chunk(chunk)
        if processed_data:
            yield processed_data

# 事件生成器，用于流式响应，包含消息格式封装与发送
async def dify_generator_and_send(transmitter: Transmitter, request: Request, question: str,user_id: str, agent_id: str, conversation_id: str):
    try: 
        yield transmitter.start()

       
        session_id = CommonUtils.generate_session_id(user_id, agent_id,conversation_id)
        logger.info(f"会话开始：session_id: {session_id}")

        dify_client = DifyClient(user_id=user_id, agent_id=agent_id)
        
        first_chunk = True
        async for processed_data, is_last in stream_with_last_(
            process_and_filter_chunks(dify_client.chat_stream(question=question, session_id=session_id))
        ):
            #logger.info(f"发送 chunk: {processed_data}, is_last: {is_last}")
            yield transmitter.send_message(
                data=processed_data,
                package_type=0,
                is_last=is_last,
                is_new_package=first_chunk,
            )
            first_chunk = False
        
        logger.info(f"会话结束：session_id: {session_id}")
        yield await transmitter.end()
    except Exception as e:
        error_msg = f"内部服务异常: {str(e)}"
        logger.error(error_msg, exc_info=True)  # 记录完整堆栈信息

        # 发送错误消息作为 chunk
        error_content = f"[ERROR] {error_msg}"
        yield transmitter.send_message(
            data=error_content,
            package_type=0,
            is_last=True,
            is_new_package=False
        )

        # 确保结束会话
        yield await transmitter.end()
    