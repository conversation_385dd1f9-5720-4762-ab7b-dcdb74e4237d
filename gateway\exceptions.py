"""
Gateway Exception Handling Module

Defines custom exceptions and error handlers for the gateway.

@Author: Enhanced by AI Assistant
"""

from typing import Dict, Any
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.routing import APIRouter

from core.config.app_logger import logger


class GatewayException(Exception):
    """Base exception for gateway errors."""
    
    def __init__(self, message: str, status_code: int = 500, details: Dict[str, Any] = None):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class ServiceNotFoundError(GatewayException):
    """Raised when a requested service is not found."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, status_code=404, details=details)


class ServiceUnavailableError(GatewayException):
    """Raised when a service is unavailable."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, status_code=503, details=details)


class RequestTimeoutError(GatewayException):
    """Raised when a request times out."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, status_code=504, details=details)


class CircuitBreakerOpenError(GatewayException):
    """Raised when circuit breaker is open."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, status_code=503, details=details)


class RateLimitExceededError(GatewayException):
    """Raised when rate limit is exceeded."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, status_code=429, details=details)


class AuthenticationError(GatewayException):
    """Raised when authentication fails."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, status_code=401, details=details)


class AuthorizationError(GatewayException):
    """Raised when authorization fails."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, status_code=403, details=details)


class RequestValidationError(GatewayException):
    """Raised when request validation fails."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, status_code=400, details=details)


def create_error_response(
    status_code: int,
    message: str,
    details: Dict[str, Any] = None,
    request_id: str = None
) -> JSONResponse:
    """Create a standardized error response."""
    
    error_response = {
        "error": {
            "code": status_code,
            "message": message,
            "timestamp": logger._core.handlers[0].sink.write.__self__.name if hasattr(logger, '_core') else None,
        }
    }
    
    if details:
        error_response["error"]["details"] = details
    
    if request_id:
        error_response["error"]["request_id"] = request_id
    
    return JSONResponse(
        status_code=status_code,
        content=error_response
    )


async def gateway_exception_handler(request: Request, exc: GatewayException) -> JSONResponse:
    """Handle gateway-specific exceptions."""
    
    request_id = getattr(request.state, "request_id", None)
    
    logger.error(
        f"Gateway exception: {exc.message}",
        extra={
            "exception_type": type(exc).__name__,
            "status_code": exc.status_code,
            "details": exc.details,
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method
        }
    )
    
    return create_error_response(
        status_code=exc.status_code,
        message=exc.message,
        details=exc.details,
        request_id=request_id
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTP exceptions."""
    
    request_id = getattr(request.state, "request_id", None)
    
    logger.warning(
        f"HTTP exception: {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method
        }
    )
    
    return create_error_response(
        status_code=exc.status_code,
        message=exc.detail,
        request_id=request_id
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions."""
    
    request_id = getattr(request.state, "request_id", None)
    
    logger.error(
        f"Unexpected exception: {str(exc)}",
        extra={
            "exception_type": type(exc).__name__,
            "request_id": request_id,
            "path": request.url.path,
            "method": request.method
        },
        exc_info=True
    )
    
    return create_error_response(
        status_code=500,
        message="Internal server error",
        details={"exception_type": type(exc).__name__} if logger.level <= 10 else None,  # DEBUG level
        request_id=request_id
    )


def setup_exception_handlers(router: APIRouter):
    """Setup exception handlers for the gateway router."""

    # Note: APIRouter doesn't support exception handlers directly
    # Exception handlers should be added to the main FastAPI app
    # This function serves as documentation for the exception handlers setup

    logger.info("Gateway exception handlers documented (add to main FastAPI app)")
