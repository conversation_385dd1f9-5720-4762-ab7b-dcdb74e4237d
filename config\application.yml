# 模型配置
ai:
  models:
    default: volcengine  # 默认模型标识符
    qwen:
      name: qwen-max
      api_key: your-qwen-api-key
      base_url: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation

    ollama:
      name: llama3
      api_key: dummy-key
      base_url: http://localhost:11434/v1

    custom:
      name: aliyun/qwen-max
      api_key: sk-cjey9DW7GA2DY4idYdWtMVXPLHHLKuKtP1rcrlUX38QzBTrH
      base_url: http://***********:3000/v1

    volcengine:
      name: volcengine/deepseek-v3
      api_key: sk-giZY3EQGWzUhpuyslUfl5KWIv7wPh0nFul0k56wems7NnTXy
      base_url: http://***********:3000/v1  

# 用户校验接口配置
userUrl: http://************:8002/api/user/basic/current

# 动态页面后端接口配置
dynamic:
  save_url: http://************:8002/api/page/system/dynamic/page/manage/insertSqlWithTempForAi
  
# 大B端配置
business_system:
  host: ************
  port: 8002

# Redis 配置
redis:
  host: ************
  port: 6379
  password: span
  db: 0

# PostgreSQL 配置
database:
  url: postgresql+asyncpg://postgres:postgres@************:5432/agent_db

# Agent 配置
agents:
  - name: dynamic-page-creator
    description: 动态页面智能体
    target: langchain


  - name: dynamic-page-qa
    description: 动态页面问答助手
    target: dify
    base_url: http://************:8091/v1/workflows/run
    api_key: app-fhU22AfNrIORU4rsjhQ2Cyk7

mcp_servers:
  - key: dynamic-page-server
    type: stdio
    url: mcpserver/mcp_server.py
    enabled: true

  - key: other-server
    type: sse
    url: http://************:5602/sse/1
    enabled: true

# Gateway 配置
gateway:
  services:
    cluster:
      name: "cluster-service"
      base_url: "http://************:8002/api"
      timeout: 30.0
      max_retries: 3
      circuit_breaker_threshold: 5
      circuit_breaker_timeout: 60.0
      health_check_path: "/health"
      enabled: true

    # 可以添加更多服务配置
    # user-service:
    #   name: "user-service"
    #   base_url: "http://user-service:8080"
    #   timeout: 15.0
    #   max_retries: 2
    #   enabled: true

  rate_limit:
    enabled: true
    requests_per_minute: 100
    burst_size: 20
    key_func: "ip"  # ip, user, or custom

  security:
    cors_enabled: true
    cors_origins: ["*"]
    cors_methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
    cors_headers: ["*"]
    max_request_size: 10485760  # 10MB
    require_auth: true
    allowed_user_agents: null
    blocked_ips: []

  monitoring:
    metrics_enabled: true
    tracing_enabled: true
    health_check_enabled: true
    log_requests: true
    log_responses: false
    log_headers: false
    performance_monitoring: true

  default_timeout: 30.0
  max_concurrent_requests: 1000

