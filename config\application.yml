# 模型配置
ai:
  models:
    default: volcengine  # 默认模型标识符
    qwen:
      name: qwen-max
      api_key: your-qwen-api-key
      base_url: https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation

    ollama:
      name: llama3
      api_key: dummy-key
      base_url: http://localhost:11434/v1

    custom:
      name: aliyun/qwen-max
      api_key: sk-cjey9DW7GA2DY4idYdWtMVXPLHHLKuKtP1rcrlUX38QzBTrH
      base_url: http://***********:3000/v1

    volcengine:
      name: volcengine/deepseek-v3
      api_key: sk-giZY3EQGWzUhpuyslUfl5KWIv7wPh0nFul0k56wems7NnTXy
      base_url: http://***********:3000/v1  

# 用户校验接口配置
userUrl: http://************:8002/api/user/basic/current

# 动态页面后端接口配置
dynamic:
  save_url: http://************:8002/api/page/system/dynamic/page/manage/insertSqlWithTempForAi
  
# 大B端配置
business_system:
  host: ************
  port: 8002

# Redis 配置
redis:
  host: ************
  port: 6379
  password: span
  db: 0

# PostgreSQL 配置
database:
  url: postgresql+asyncpg://postgres:postgres@************:5432/agent_db

# Agent 配置
agents:
  - name: dynamic-page-creator
    description: 动态页面智能体
    target: langchain


  - name: dynamic-page-qa
    description: 动态页面问答助手
    target: dify
    base_url: http://************:8091/v1/workflows/run
    api_key: app-fhU22AfNrIORU4rsjhQ2Cyk7

mcp_servers:
  - key: dynamic-page-server
    type: stdio
    url: mcpserver/mcp_server.py
    enabled: true

  - key: other-server
    type: sse
    url: http://************:5602/sse/1
    enabled: true
    

