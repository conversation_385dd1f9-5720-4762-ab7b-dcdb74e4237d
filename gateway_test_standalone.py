#!/usr/bin/env python3
"""
Standalone test for gateway components that don't require full app context.
"""

def test_circuit_breaker():
    """Test circuit breaker functionality."""
    print("🔍 Testing Circuit Breaker...")
    
    from gateway.utils import CircuitBreaker, CircuitBreakerState
    
    # Create circuit breaker
    cb = CircuitBreaker(failure_threshold=2, timeout=1.0)
    
    # Test initial state
    assert cb.state == CircuitBreakerState.CLOSED
    assert not cb.is_open()
    print("✅ Initial state: CLOSED")
    
    # Test failure recording
    cb.record_failure()
    assert cb.state == CircuitBreakerState.CLOSED
    print("✅ First failure recorded, still CLOSED")
    
    cb.record_failure()
    assert cb.state == CircuitBreakerState.OPEN
    assert cb.is_open()
    print("✅ Second failure recorded, now OPEN")
    
    # Test statistics
    stats = cb.get_stats()
    assert stats["failure_count"] == 2
    assert stats["state"] == "open"
    print("✅ Statistics working correctly")
    
    print("🎉 Circuit Breaker tests passed!\n")


def test_request_metrics():
    """Test request metrics functionality."""
    print("🔍 Testing Request Metrics...")
    
    from gateway.utils import RequestMetrics
    
    # Create metrics collector
    metrics = RequestMetrics(max_metrics=100)
    
    # Record some requests
    metrics.record_request("service-a", "GET", 200, 0.1, True)
    metrics.record_request("service-a", "POST", 201, 0.2, True)
    metrics.record_request("service-a", "GET", 500, 0.3, False)
    metrics.record_request("service-b", "GET", 200, 0.15, True)
    
    # Test service metrics
    service_a_metrics = metrics.get_service_metrics("service-a")
    assert service_a_metrics["total_requests"] == 3
    assert service_a_metrics["success_rate"] == 2/3  # 2 successful out of 3
    assert service_a_metrics["error_rate"] == 1/3    # 1 failed out of 3
    print("✅ Service-specific metrics working")
    
    # Test overall metrics
    overall = metrics.get_overall_metrics()
    assert overall["total_requests"] == 4
    assert overall["success_rate"] == 3/4  # 3 successful out of 4
    print("✅ Overall metrics working")
    
    print("🎉 Request Metrics tests passed!\n")


def test_utility_functions():
    """Test utility functions."""
    print("🔍 Testing Utility Functions...")
    
    from gateway.utils import generate_request_id, sanitize_headers, format_duration
    
    # Test request ID generation
    id1 = generate_request_id()
    id2 = generate_request_id()
    assert id1 != id2
    assert len(id1) == 36  # UUID format
    print("✅ Request ID generation working")
    
    # Test header sanitization
    headers = {
        "authorization": "Bearer secret",
        "content-type": "application/json",
        "x-api-key": "secret-key"
    }
    sanitized = sanitize_headers(headers)
    assert sanitized["authorization"] == "***REDACTED***"
    assert sanitized["content-type"] == "application/json"
    assert sanitized["x-api-key"] == "***REDACTED***"
    print("✅ Header sanitization working")
    
    # Test duration formatting
    assert format_duration(0.001) == "1.00ms"
    assert format_duration(1.5) == "1.50s"
    assert format_duration(90.5) == "1m 30.50s"
    print("✅ Duration formatting working")
    
    print("🎉 Utility Functions tests passed!\n")


def test_models():
    """Test Pydantic models."""
    print("🔍 Testing Pydantic Models...")
    
    from gateway.models import ServiceConfigModel, RateLimitConfigModel
    
    # Test service config model
    service_config = ServiceConfigModel(
        name="test-service",
        base_url="http://test:8080",
        timeout=30.0,
        max_retries=3
    )
    assert service_config.name == "test-service"
    assert service_config.timeout == 30.0
    print("✅ Service config model working")
    
    # Test rate limit config model
    rate_limit_config = RateLimitConfigModel(
        enabled=True,
        requests_per_minute=100,
        key_func="ip"
    )
    assert rate_limit_config.enabled is True
    assert rate_limit_config.requests_per_minute == 100
    print("✅ Rate limit config model working")
    
    # Test validation
    try:
        ServiceConfigModel(
            name="invalid",
            base_url="http://test:8080",
            timeout=-1.0  # Invalid timeout
        )
        assert False, "Should have raised validation error"
    except ValueError:
        print("✅ Model validation working")
    
    print("🎉 Pydantic Models tests passed!\n")


def test_exceptions():
    """Test custom exceptions."""
    print("🔍 Testing Custom Exceptions...")
    
    from gateway.exceptions import (
        ServiceNotFoundError,
        ServiceUnavailableError,
        CircuitBreakerOpenError
    )
    
    # Test exception creation
    try:
        raise ServiceNotFoundError("Test service not found")
    except ServiceNotFoundError as e:
        assert e.status_code == 404
        assert "not found" in e.message
        print("✅ ServiceNotFoundError working")
    
    try:
        raise ServiceUnavailableError("Test service unavailable")
    except ServiceUnavailableError as e:
        assert e.status_code == 503
        print("✅ ServiceUnavailableError working")
    
    try:
        raise CircuitBreakerOpenError("Circuit breaker open")
    except CircuitBreakerOpenError as e:
        assert e.status_code == 503
        print("✅ CircuitBreakerOpenError working")
    
    print("🎉 Custom Exceptions tests passed!\n")


if __name__ == "__main__":
    print("🚀 Starting Standalone Gateway Tests\n")
    
    try:
        test_circuit_breaker()
        test_request_metrics()
        test_utility_functions()
        test_models()
        test_exceptions()
        
        print("🎊 All standalone tests passed!")
        print("✨ Gateway module components are working correctly!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
