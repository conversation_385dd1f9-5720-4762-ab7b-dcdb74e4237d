"""
Gateway Configuration Module

Manages gateway-specific configuration including service routing,
timeouts, rate limiting, and security settings.

@Author: Enhanced by AI Assistant
"""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field, validator
from core.config.app_config import config


class ServiceConfig(BaseModel):
    """Configuration for a single service."""
    name: str
    base_url: str
    timeout: float = 30.0
    max_retries: int = 3
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: float = 60.0
    health_check_path: Optional[str] = "/health"
    enabled: bool = True


class RateLimitConfig(BaseModel):
    """Rate limiting configuration."""
    enabled: bool = True
    requests_per_minute: int = 100
    burst_size: int = 20
    key_func: str = "ip"  # ip, user, or custom


class SecurityConfig(BaseModel):
    """Security configuration."""
    cors_enabled: bool = True
    cors_origins: List[str] = ["*"]
    cors_methods: List[str] = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
    cors_headers: List[str] = ["*"]
    max_request_size: int = 10 * 1024 * 1024  # 10MB
    require_auth: bool = True
    allowed_user_agents: Optional[List[str]] = None
    blocked_ips: List[str] = []


class MonitoringConfig(BaseModel):
    """Monitoring and observability configuration."""
    metrics_enabled: bool = True
    tracing_enabled: bool = True
    health_check_enabled: bool = True
    log_requests: bool = True
    log_responses: bool = False  # Be careful with sensitive data
    log_headers: bool = False
    performance_monitoring: bool = True


class GatewayConfig(BaseModel):
    """Main gateway configuration."""
    services: Dict[str, ServiceConfig] = Field(default_factory=dict)
    rate_limit: RateLimitConfig = Field(default_factory=RateLimitConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    default_timeout: float = 30.0
    max_concurrent_requests: int = 1000
    
    @validator('services', pre=True)
    def validate_services(cls, v):
        if isinstance(v, dict):
            return v
        return {}


def load_gateway_config() -> GatewayConfig:
    """Load gateway configuration from the main config."""
    
    # Default service configurations
    services = {}
    
    # Add cluster service from existing config
    if hasattr(config, 'business_system'):
        cluster_url = f"http://{config.business_system.host}:{config.business_system.port}/api"
        services["cluster"] = ServiceConfig(
            name="cluster",
            base_url=cluster_url,
            timeout=30.0,
            max_retries=3,
            enabled=True
        )
    
    # Load additional services from config if available
    gateway_config_data = config.full_config.get("gateway", {})
    
    # Override with config file settings if present
    if "services" in gateway_config_data:
        for service_name, service_data in gateway_config_data["services"].items():
            services[service_name] = ServiceConfig(
                name=service_name,
                **service_data
            )
    
    # Rate limiting configuration
    rate_limit_config = RateLimitConfig(
        **gateway_config_data.get("rate_limit", {})
    )
    
    # Security configuration
    security_config = SecurityConfig(
        **gateway_config_data.get("security", {})
    )
    
    # Monitoring configuration
    monitoring_config = MonitoringConfig(
        **gateway_config_data.get("monitoring", {})
    )
    
    return GatewayConfig(
        services=services,
        rate_limit=rate_limit_config,
        security=security_config,
        monitoring=monitoring_config,
        default_timeout=gateway_config_data.get("default_timeout", 30.0),
        max_concurrent_requests=gateway_config_data.get("max_concurrent_requests", 1000)
    )


# Global gateway configuration instance
gateway_config = load_gateway_config()
