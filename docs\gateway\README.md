# Enhanced Gateway Module

The enhanced gateway module provides a robust API gateway with improved error handling, logging, security, monitoring, and resilience patterns.

## Features

### 🚀 Core Features
- **Service Routing**: Dynamic service discovery and routing
- **Load Balancing**: Distribute requests across service instances
- **Authentication**: Enhanced authentication with caching
- **Authorization**: Role and permission-based access control
- **Request/Response Logging**: Comprehensive request tracing

### 🛡️ Security Features
- **Rate Limiting**: Configurable rate limiting per IP/user
- **CORS Support**: Cross-origin resource sharing configuration
- **Request Validation**: Input validation and sanitization
- **IP Blocking**: Block malicious IP addresses
- **User Agent Filtering**: Filter requests by user agent

### 🔧 Resilience Features
- **Circuit Breaker**: Prevent cascading failures
- **Timeout Management**: Configurable request timeouts
- **Retry Logic**: Automatic retry with exponential backoff
- **Health Checks**: Service health monitoring
- **Graceful Degradation**: Fallback mechanisms

### 📊 Monitoring Features
- **Metrics Collection**: Request metrics and performance data
- **Health Endpoints**: Gateway and service health status
- **Performance Monitoring**: Response time and throughput tracking
- **Error Tracking**: Detailed error logging and analysis

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client        │    │   Gateway       │    │   Services      │
│                 │    │                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │  Request  │──┼────┼─▶│Middleware │  │    │  │ Service A │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│                 │    │         │       │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │ Response  │◀─┼────┼──│  Router   │──┼────┼─▶│ Service B │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Configuration

### Gateway Configuration

The gateway can be configured through the main application configuration file:

```yaml
gateway:
  services:
    cluster:
      name: "cluster-service"
      base_url: "http://cluster-service:8080/api"
      timeout: 30.0
      max_retries: 3
      circuit_breaker_threshold: 5
      circuit_breaker_timeout: 60.0
      enabled: true
    
    user-service:
      name: "user-service"
      base_url: "http://user-service:8080"
      timeout: 15.0
      max_retries: 2
      enabled: true
  
  rate_limit:
    enabled: true
    requests_per_minute: 100
    burst_size: 20
    key_func: "ip"  # ip, user, or custom
  
  security:
    cors_enabled: true
    cors_origins: ["*"]
    max_request_size: 10485760  # 10MB
    require_auth: true
    blocked_ips: []
  
  monitoring:
    metrics_enabled: true
    tracing_enabled: true
    log_requests: true
    log_responses: false
    performance_monitoring: true
  
  default_timeout: 30.0
  max_concurrent_requests: 1000
```

### Service Configuration

Each service can be configured with:

- **name**: Service identifier
- **base_url**: Service base URL
- **timeout**: Request timeout in seconds
- **max_retries**: Maximum retry attempts
- **circuit_breaker_threshold**: Failure threshold for circuit breaker
- **circuit_breaker_timeout**: Circuit breaker timeout in seconds
- **health_check_path**: Health check endpoint path
- **enabled**: Whether the service is enabled

## API Endpoints

### Gateway Endpoints

#### Health Check
```http
GET /gateway/health
```

Returns the health status of the gateway and all configured services.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": **********.0,
  "services": {
    "cluster": {
      "service": "cluster",
      "status": "enabled",
      "base_url": "http://cluster-service:8080/api",
      "circuit_breaker": "closed",
      "metrics": {
        "total_requests": 100,
        "success_rate": 0.95,
        "average_duration": 0.2
      }
    }
  },
  "version": "2.0.0",
  "uptime": 3600.0
}
```

#### Metrics
```http
GET /gateway/metrics?time_window=300
```

Returns gateway metrics for the specified time window.

**Parameters:**
- `time_window` (optional): Time window in seconds (default: 300)

**Response:**
```json
{
  "total_requests": 1000,
  "success_rate": 0.95,
  "error_rate": 0.05,
  "average_duration": 0.25,
  "time_window": 300,
  "services": {
    "cluster": {
      "total_requests": 800,
      "success_rate": 0.96,
      "average_duration": 0.22
    }
  },
  "status_codes": {
    "200": 950,
    "404": 30,
    "500": 20
  }
}
```

#### Service Health
```http
GET /gateway/services/{service_name}/health
```

Returns health status for a specific service.

### Service Routing

All service requests are routed through:

```http
{METHOD} /gateway/{service}/{path}
```

**Examples:**
- `GET /gateway/cluster/users` → `GET http://cluster-service:8080/api/users`
- `POST /gateway/user-service/auth/login` → `POST http://user-service:8080/auth/login`

## Usage Examples

### Basic Request Routing

```python
import httpx

# Request to cluster service
response = await httpx.get(
    "http://gateway:8000/gateway/cluster/users",
    headers={"Authorization": "Bearer your-token"}
)
```

### Health Check

```python
import httpx

# Check gateway health
health = await httpx.get("http://gateway:8000/gateway/health")
print(health.json())
```

### Metrics Monitoring

```python
import httpx

# Get metrics for last 5 minutes
metrics = await httpx.get("http://gateway:8000/gateway/metrics?time_window=300")
print(f"Success rate: {metrics.json()['success_rate']}")
```

## Error Handling

The gateway provides standardized error responses:

```json
{
  "error": {
    "code": 503,
    "message": "Service unavailable",
    "details": {
      "service": "cluster",
      "reason": "Circuit breaker open"
    },
    "request_id": "req-123456",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

### Error Codes

- **400**: Bad Request - Invalid request format
- **401**: Unauthorized - Authentication required
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Service or endpoint not found
- **429**: Too Many Requests - Rate limit exceeded
- **500**: Internal Server Error - Gateway error
- **503**: Service Unavailable - Target service down
- **504**: Gateway Timeout - Request timeout

## Monitoring and Observability

### Metrics

The gateway collects various metrics:

- **Request Count**: Total number of requests
- **Success Rate**: Percentage of successful requests
- **Error Rate**: Percentage of failed requests
- **Response Time**: Average response time
- **Status Code Distribution**: Count by HTTP status code
- **Service-specific Metrics**: Per-service performance data

### Logging

Request and response logging includes:

- Request ID for tracing
- HTTP method and path
- Client IP address
- User information (if authenticated)
- Response status and duration
- Error details and stack traces

### Health Checks

- Gateway health status
- Service availability
- Circuit breaker states
- Performance metrics
- System resource usage

## Testing

### Running Tests

```bash
# Run all gateway tests
python tests/gateway/run_tests.py

# Run specific test file
python tests/gateway/run_tests.py test_routing.py

# Run specific test function
python tests/gateway/run_tests.py test_routing.py TestGatewayRouter::test_route_request
```

### Test Coverage

The test suite covers:

- Request routing and forwarding
- Circuit breaker functionality
- Rate limiting
- Authentication and authorization
- Error handling
- Metrics collection
- Health checks

## Performance Considerations

### Optimization Tips

1. **Connection Pooling**: Use HTTP connection pooling for better performance
2. **Caching**: Cache authentication results and service configurations
3. **Async Processing**: Use async/await for non-blocking operations
4. **Resource Limits**: Configure appropriate timeouts and limits
5. **Monitoring**: Monitor performance metrics and adjust configuration

### Scaling

- **Horizontal Scaling**: Deploy multiple gateway instances
- **Load Balancing**: Use load balancer in front of gateway instances
- **Service Discovery**: Implement dynamic service discovery
- **Caching**: Use Redis for distributed caching

## Security Best Practices

1. **Authentication**: Always require authentication for sensitive endpoints
2. **Rate Limiting**: Implement rate limiting to prevent abuse
3. **Input Validation**: Validate all incoming requests
4. **HTTPS**: Use HTTPS for all communications
5. **Logging**: Log security events for monitoring
6. **Updates**: Keep dependencies updated

## Troubleshooting

### Common Issues

1. **Service Unavailable**: Check service health and circuit breaker status
2. **High Latency**: Monitor metrics and check service performance
3. **Authentication Failures**: Verify token validity and user permissions
4. **Rate Limiting**: Check rate limit configuration and usage patterns

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.getLogger("gateway").setLevel(logging.DEBUG)
```
