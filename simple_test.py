#!/usr/bin/env python3
"""Simple test to check basic imports."""

try:
    print("Testing basic imports...")
    
    # Test if we can import the core config first
    from core.config.app_config import config
    print("✅ Core config imported")
    
    # Test gateway config
    from gateway.config import GatewayConfig
    print("✅ Gateway config class imported")
    
    # Test utils
    from gateway.utils import CircuitBreaker
    print("✅ Circuit breaker imported")
    
    print("🎉 Basic imports successful!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
