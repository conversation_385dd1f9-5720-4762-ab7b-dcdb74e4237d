"""
Tests for Gateway Utilities Module

@Author: Enhanced by AI Assistant
"""

import pytest
import time
from unittest.mock import patch

from gateway.utils import (
    CircuitBreaker, 
    CircuitBreakerState, 
    RequestMetrics, 
    generate_request_id,
    sanitize_headers,
    format_duration
)


class TestCircuitBreaker:
    """Test cases for CircuitBreaker class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=3,
            timeout=10.0,
            success_threshold=2
        )
    
    def test_initial_state(self):
        """Test circuit breaker initial state."""
        assert self.circuit_breaker.state == CircuitBreakerState.CLOSED
        assert not self.circuit_breaker.is_open()
        assert not self.circuit_breaker.is_half_open()
        assert self.circuit_breaker.stats.failure_count == 0
        assert self.circuit_breaker.stats.success_count == 0
    
    def test_record_success_closed_state(self):
        """Test recording success in closed state."""
        self.circuit_breaker.record_success()
        
        assert self.circuit_breaker.stats.success_count == 1
        assert self.circuit_breaker.stats.last_success_time is not None
        assert self.circuit_breaker.state == CircuitBreakerState.CLOSED
    
    def test_record_failure_closed_state(self):
        """Test recording failure in closed state."""
        self.circuit_breaker.record_failure()
        
        assert self.circuit_breaker.stats.failure_count == 1
        assert self.circuit_breaker.stats.last_failure_time is not None
        assert self.circuit_breaker.state == CircuitBreakerState.CLOSED
    
    def test_transition_to_open(self):
        """Test transition to open state after threshold failures."""
        # Record failures up to threshold
        for _ in range(self.circuit_breaker.failure_threshold):
            self.circuit_breaker.record_failure()
        
        assert self.circuit_breaker.state == CircuitBreakerState.OPEN
        assert self.circuit_breaker.is_open()
        assert self.circuit_breaker.stats.failure_count == self.circuit_breaker.failure_threshold
    
    def test_transition_to_half_open(self):
        """Test transition to half-open state after timeout."""
        # Force circuit breaker to open
        for _ in range(self.circuit_breaker.failure_threshold):
            self.circuit_breaker.record_failure()
        
        assert self.circuit_breaker.is_open()
        
        # Mock time to simulate timeout
        with patch('time.time') as mock_time:
            # Set current time to be after timeout
            mock_time.return_value = self.circuit_breaker.stats.state_change_time + self.circuit_breaker.timeout + 1
            
            # Check if circuit breaker transitions to half-open
            is_open = self.circuit_breaker.is_open()
            assert not is_open
            assert self.circuit_breaker.state == CircuitBreakerState.HALF_OPEN
    
    def test_half_open_to_closed_on_success(self):
        """Test transition from half-open to closed on successful requests."""
        # Force to half-open state
        self.circuit_breaker.state = CircuitBreakerState.HALF_OPEN
        
        # Record successful requests up to success threshold
        for _ in range(self.circuit_breaker.success_threshold):
            self.circuit_breaker.record_success()
        
        assert self.circuit_breaker.state == CircuitBreakerState.CLOSED
        assert self.circuit_breaker.stats.failure_count == 0
    
    def test_half_open_to_open_on_failure(self):
        """Test transition from half-open to open on failure."""
        # Force to half-open state
        self.circuit_breaker.state = CircuitBreakerState.HALF_OPEN
        
        # Record a failure
        self.circuit_breaker.record_failure()
        
        assert self.circuit_breaker.state == CircuitBreakerState.OPEN
        assert self.circuit_breaker.is_open()
    
    def test_get_stats(self):
        """Test circuit breaker statistics retrieval."""
        # Record some operations
        self.circuit_breaker.record_success()
        self.circuit_breaker.record_failure()
        
        stats = self.circuit_breaker.get_stats()
        
        assert stats["state"] == CircuitBreakerState.CLOSED.value
        assert stats["success_count"] == 1
        assert stats["failure_count"] == 1
        assert stats["failure_threshold"] == 3
        assert stats["timeout"] == 10.0
        assert "last_success_time" in stats
        assert "last_failure_time" in stats
        assert "state_change_time" in stats


class TestRequestMetrics:
    """Test cases for RequestMetrics class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.metrics = RequestMetrics(max_metrics=100)
    
    def test_record_request(self):
        """Test recording a request metric."""
        self.metrics.record_request(
            service="test-service",
            method="GET",
            status_code=200,
            duration=0.5,
            success=True
        )
        
        assert len(self.metrics.metrics) == 1
        assert len(self.metrics.service_metrics["test-service"]) == 1
        
        metric = self.metrics.metrics[0]
        assert metric.service == "test-service"
        assert metric.method == "GET"
        assert metric.status_code == 200
        assert metric.duration == 0.5
        assert metric.success is True
    
    def test_get_service_metrics_empty(self):
        """Test getting metrics for service with no requests."""
        metrics = self.metrics.get_service_metrics("nonexistent-service")
        
        assert metrics["total_requests"] == 0
        assert metrics["success_rate"] == 0.0
        assert metrics["average_duration"] == 0.0
        assert metrics["error_rate"] == 0.0
        assert metrics["status_codes"] == {}
    
    def test_get_service_metrics_with_data(self):
        """Test getting metrics for service with requests."""
        # Record some requests
        self.metrics.record_request("test", "GET", 200, 0.1, True)
        self.metrics.record_request("test", "POST", 201, 0.2, True)
        self.metrics.record_request("test", "GET", 500, 0.3, False)
        
        metrics = self.metrics.get_service_metrics("test")
        
        assert metrics["total_requests"] == 3
        assert metrics["success_rate"] == 2/3  # 2 successful out of 3
        assert metrics["error_rate"] == 1/3    # 1 failed out of 3
        assert metrics["average_duration"] == 0.2  # (0.1 + 0.2 + 0.3) / 3
        assert metrics["status_codes"][200] == 1
        assert metrics["status_codes"][201] == 1
        assert metrics["status_codes"][500] == 1
    
    def test_get_service_metrics_time_window(self):
        """Test getting metrics within a time window."""
        # Record an old request (outside time window)
        with patch('time.time') as mock_time:
            mock_time.return_value = 1000.0
            self.metrics.record_request("test", "GET", 200, 0.1, True)
        
        # Record a recent request (within time window)
        with patch('time.time') as mock_time:
            mock_time.return_value = 1500.0
            self.metrics.record_request("test", "POST", 201, 0.2, True)
            
            # Get metrics with 300 second window (should only include recent request)
            metrics = self.metrics.get_service_metrics("test", time_window=300)
            
            assert metrics["total_requests"] == 1
            assert metrics["status_codes"][201] == 1
            assert 200 not in metrics["status_codes"]
    
    def test_get_overall_metrics(self):
        """Test getting overall metrics across all services."""
        # Record requests for multiple services
        self.metrics.record_request("service1", "GET", 200, 0.1, True)
        self.metrics.record_request("service2", "POST", 201, 0.2, True)
        self.metrics.record_request("service1", "GET", 500, 0.3, False)
        
        metrics = self.metrics.get_overall_metrics()
        
        assert metrics["total_requests"] == 3
        assert metrics["success_rate"] == 2/3
        assert metrics["average_duration"] == 0.2
        assert "service1" in metrics["services"]
        assert "service2" in metrics["services"]
    
    def test_max_metrics_limit(self):
        """Test that metrics collection respects max limit."""
        metrics = RequestMetrics(max_metrics=2)
        
        # Record more requests than the limit
        metrics.record_request("test", "GET", 200, 0.1, True)
        metrics.record_request("test", "GET", 200, 0.2, True)
        metrics.record_request("test", "GET", 200, 0.3, True)
        
        # Should only keep the last 2 requests
        assert len(metrics.metrics) == 2


class TestUtilityFunctions:
    """Test cases for utility functions."""
    
    def test_generate_request_id(self):
        """Test request ID generation."""
        id1 = generate_request_id()
        id2 = generate_request_id()
        
        assert id1 != id2
        assert len(id1) > 0
        assert len(id2) > 0
        # UUIDs should be 36 characters with hyphens
        assert len(id1) == 36
        assert len(id2) == 36
    
    def test_sanitize_headers(self):
        """Test header sanitization."""
        headers = {
            "authorization": "Bearer secret-token",
            "cookie": "session=abc123",
            "x-api-key": "api-key-123",
            "content-type": "application/json",
            "user-agent": "test-client"
        }
        
        sanitized = sanitize_headers(headers)
        
        assert sanitized["authorization"] == "***REDACTED***"
        assert sanitized["cookie"] == "***REDACTED***"
        assert sanitized["x-api-key"] == "***REDACTED***"
        assert sanitized["content-type"] == "application/json"
        assert sanitized["user-agent"] == "test-client"
    
    def test_format_duration(self):
        """Test duration formatting."""
        # Test milliseconds
        assert format_duration(0.001) == "1.00ms"
        assert format_duration(0.5) == "500.00ms"
        
        # Test seconds
        assert format_duration(1.5) == "1.50s"
        assert format_duration(30.0) == "30.00s"
        
        # Test minutes
        assert format_duration(60.0) == "1m 0.00s"
        assert format_duration(90.5) == "1m 30.50s"
        assert format_duration(125.75) == "2m 5.75s"
