#!/usr/bin/env python3
"""
Simple test script to verify gateway imports work correctly.
"""

def test_gateway_imports():
    """Test that all gateway components can be imported."""
    try:
        print("🔍 Testing gateway imports...")
        
        # Test basic imports
        from gateway.config import gateway_config
        print("✅ Gateway config imported successfully")
        
        from gateway.utils import CircuitBreaker, RequestMetrics
        print("✅ Gateway utilities imported successfully")
        
        from gateway.exceptions import GatewayException
        print("✅ Gateway exceptions imported successfully")
        
        from gateway.routing import GatewayRouter
        print("✅ Gateway router imported successfully")
        
        from gateway.main import router
        print("✅ Main router imported successfully")
        
        # Test configuration
        print(f"📊 Services configured: {list(gateway_config.services.keys())}")
        print(f"🔧 Rate limiting enabled: {gateway_config.rate_limit.enabled}")
        print(f"🛡️ Security auth required: {gateway_config.security.require_auth}")
        
        # Test router configuration
        print(f"🌐 Router prefix: {router.prefix}")
        print(f"🏷️ Router tags: {router.tags}")
        
        # Test utility classes
        cb = CircuitBreaker(failure_threshold=3, timeout=10.0)
        print(f"⚡ Circuit breaker created: {cb.state.value}")
        
        metrics = RequestMetrics()
        print(f"📈 Metrics collector created: max_metrics={metrics.max_metrics}")
        
        print("\n🎉 All gateway components imported and tested successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Import test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gateway_functionality():
    """Test basic gateway functionality."""
    try:
        print("\n🔍 Testing gateway functionality...")
        
        from gateway.routing import GatewayRouter
        from gateway.utils import CircuitBreaker, RequestMetrics
        
        # Test circuit breaker
        cb = CircuitBreaker(failure_threshold=2, timeout=5.0)
        
        # Test success recording
        cb.record_success()
        assert cb.stats.success_count == 1
        print("✅ Circuit breaker success recording works")
        
        # Test failure recording
        cb.record_failure()
        cb.record_failure()  # Should open circuit breaker
        assert cb.is_open()
        print("✅ Circuit breaker failure handling works")
        
        # Test metrics
        metrics = RequestMetrics()
        metrics.record_request("test-service", "GET", 200, 0.1, True)
        metrics.record_request("test-service", "POST", 500, 0.5, False)
        
        service_metrics = metrics.get_service_metrics("test-service")
        assert service_metrics["total_requests"] == 2
        assert service_metrics["success_rate"] == 0.5
        print("✅ Metrics collection works")
        
        # Test gateway router
        router = GatewayRouter()
        health = router.get_service_health("nonexistent")
        assert health["status"] == "not_found"
        print("✅ Gateway router health check works")
        
        print("\n🎉 All gateway functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Starting Gateway Module Tests\n")
    
    # Test imports
    import_success = test_gateway_imports()
    
    if import_success:
        # Test functionality
        func_success = test_gateway_functionality()
        
        if func_success:
            print("\n🎊 All tests passed! Gateway module is ready for use.")
            exit(0)
        else:
            print("\n💥 Functionality tests failed!")
            exit(1)
    else:
        print("\n💥 Import tests failed!")
        exit(1)
