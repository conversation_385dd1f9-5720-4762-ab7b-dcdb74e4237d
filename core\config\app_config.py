# 加载系统配置信息，环境变量
# <AUTHOR> xiangjh
import os
import yaml
from pathlib import Path
import logging
from pydantic import BaseModel, Field, model_validator
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)

class RedisConfig(BaseModel):
    host: str = "127.0.0.1"
    port: int = 6379
    password: Optional[str] = None
    db: int = 0
    decode_responses: bool = True

    @model_validator(mode="after")
    def validate_password(self):
        if self.password is None:
            raw_pass = os.getenv("REDIS_PASSWORD")
            if raw_pass:
                self.password = raw_pass
        return self

class PostgreSQLConfig(BaseModel):
    url: str = "postgresql+asyncpg://postgres:postgres@localhost:5432/agent_db"

class ModelConfig(BaseModel):
    name: str
    api_key: str
    base_url: str
    temperature: float = 0.0

class SystemConfig(BaseModel):
    host: str
    port: int = 8000

class AgentConfig(BaseModel):
    name: str
    description: str
    target: str
    base_url: Optional[str] = None
    api_key: Optional[str] = None

class McpServer(BaseModel):
    key: str
    type: str
    url: str
    enabled: bool 

class AppConfig(BaseModel):
    redis: RedisConfig
    model: ModelConfig
    full_config: Dict[str, Any] = Field(default_factory=dict)
    business_system: SystemConfig
    database: PostgreSQLConfig
    agents: List[AgentConfig] = []
    mcp_servers: List[McpServer] = []

def load_config() -> AppConfig:
    # 从环境变量中获取配置文件路径
    config_path = os.getenv("CONFIG_PATH", "config/application.yml")
    config_path = Path.joinpath(Path.cwd(), config_path)
    with open(config_path, "r", encoding="utf-8") as f:
        full_config = yaml.safe_load(f)

    # 加载 Redis 配置（优先级：环境变量 > yml）
    redis_config = full_config.get("redis", {})
    redis_data = {
        "host": redis_config.get("host", "127.0.0.1"),
        "port": int(redis_config.get("port", 6379)),
        "password": redis_config.get("password"),
        "db": int(redis_config.get("db", 0)),
        "decode_responses": redis_config.get("decode_responses", True),
    }

    database_config = full_config.get("database", {})
    database_data = {
        "url": database_config.get("url")
    }

    # 加载 AI 模型配置
    ai_config = full_config.get("ai", {})
    default_model_name = ai_config["models"]["default"]
    selected_model = full_config["ai"]["models"].get(default_model_name, {})

    model_data = {
        "name": selected_model.get("name"),
        "api_key": selected_model.get("api_key"),
        "base_url": selected_model.get("base_url"),
        "temperature": float(selected_model.get("temperature", 0.0)),
    }

    if not model_data["name"]:
        raise ValueError("未找到有效的模型名称，请检查配置文件或环境变量")

    system_config = full_config.get("business_system", {})
    system_data = {
        "host": system_config.get("host"),
        "port": system_config.get("port"),
    }
    # 返回 Pydantic 类型安全的配置对象
    return AppConfig(
        redis=RedisConfig(**redis_data),
        database=PostgreSQLConfig(**database_data),
        model=ModelConfig(**model_data),
        business_system=SystemConfig(**system_data),
        full_config=full_config,
        agents=[AgentConfig(**agent) for agent in full_config.get("agents", [])],
        mcp_servers=[McpServer(**server) for server in full_config.get("mcp_servers", [])]
    )


# 实例化全局配置
config = load_config()
