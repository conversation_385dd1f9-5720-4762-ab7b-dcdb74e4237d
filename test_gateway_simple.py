#!/usr/bin/env python3
"""
简化的Gateway模块测试 - 测试独立组件
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_circuit_breaker_standalone():
    """独立测试熔断器功能"""
    print("🔍 测试熔断器功能...")
    
    try:
        # 直接导入，避免依赖配置
        sys.path.insert(0, str(project_root / "gateway"))
        
        from utils import CircuitBreaker, CircuitBreakerState
        
        # 创建熔断器
        cb = CircuitBreaker(failure_threshold=2, timeout=1.0)
        
        # 测试初始状态
        assert cb.state == CircuitBreakerState.CLOSED
        print("✅ 初始状态正确: CLOSED")
        
        # 测试失败记录
        cb.record_failure()
        assert cb.state == CircuitBreakerState.CLOSED
        print("✅ 第一次失败，状态仍为CLOSED")
        
        cb.record_failure()
        assert cb.state == CircuitBreakerState.OPEN
        print("✅ 第二次失败，状态变为OPEN")
        
        # 测试统计
        stats = cb.get_stats()
        assert stats["failure_count"] == 2
        assert stats["state"] == "open"
        print("✅ 统计信息正确")
        
        # 测试超时转换
        print("   等待超时转换...")
        time.sleep(1.1)
        
        # 检查是否转为半开
        is_open = cb.is_open()
        if not is_open:
            assert cb.is_half_open()
            print("✅ 超时后转为半开状态")
        
        print("🎉 熔断器测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 熔断器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_request_metrics_standalone():
    """独立测试请求指标"""
    print("\n🔍 测试请求指标...")
    
    try:
        from utils import RequestMetrics
        
        metrics = RequestMetrics()
        
        # 记录测试数据
        metrics.record_request("test-service", "GET", 200, 0.1, True)
        metrics.record_request("test-service", "POST", 500, 0.2, False)
        metrics.record_request("test-service", "GET", 201, 0.15, True)
        
        # 测试服务指标
        service_metrics = metrics.get_service_metrics("test-service")
        assert service_metrics["total_requests"] == 3
        assert service_metrics["success_rate"] == 2/3  # 2成功/3总数
        print("✅ 服务指标计算正确")
        
        # 测试整体指标
        overall = metrics.get_overall_metrics()
        assert overall["total_requests"] == 3
        assert overall["success_rate"] == 2/3
        print("✅ 整体指标计算正确")
        
        print("🎉 请求指标测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 请求指标测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_utility_functions_standalone():
    """独立测试工具函数"""
    print("\n🔍 测试工具函数...")
    
    try:
        from utils import generate_request_id, sanitize_headers, format_duration
        
        # 测试ID生成
        id1 = generate_request_id()
        id2 = generate_request_id()
        assert id1 != id2
        assert len(id1) == 36
        print("✅ 请求ID生成正常")
        
        # 测试头部脱敏
        headers = {
            "authorization": "Bearer secret",
            "content-type": "application/json"
        }
        sanitized = sanitize_headers(headers)
        assert sanitized["authorization"] == "***REDACTED***"
        assert sanitized["content-type"] == "application/json"
        print("✅ 头部脱敏正常")
        
        # 测试时间格式化
        assert format_duration(0.001) == "1.00ms"
        assert format_duration(1.5) == "1.50s"
        assert format_duration(90.5) == "1m 30.50s"
        print("✅ 时间格式化正常")
        
        print("🎉 工具函数测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_exceptions_standalone():
    """独立测试异常类"""
    print("\n🔍 测试异常类...")
    
    try:
        from exceptions import (
            GatewayException, ServiceNotFoundError, 
            ServiceUnavailableError, CircuitBreakerOpenError
        )
        
        # 测试基础异常
        try:
            raise GatewayException("测试", status_code=500)
        except GatewayException as e:
            assert e.status_code == 500
            print("✅ 基础异常正常")
        
        # 测试具体异常
        try:
            raise ServiceNotFoundError("服务未找到")
        except ServiceNotFoundError as e:
            assert e.status_code == 404
            print("✅ 服务未找到异常正常")
        
        try:
            raise ServiceUnavailableError("服务不可用")
        except ServiceUnavailableError as e:
            assert e.status_code == 503
            print("✅ 服务不可用异常正常")
        
        try:
            raise CircuitBreakerOpenError("熔断器打开")
        except CircuitBreakerOpenError as e:
            assert e.status_code == 503
            print("✅ 熔断器异常正常")
        
        print("🎉 异常类测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 异常类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_models_standalone():
    """独立测试模型类"""
    print("\n🔍 测试模型类...")
    
    try:
        from models import ServiceConfigModel, RateLimitConfigModel
        
        # 测试服务配置模型
        service_config = ServiceConfigModel(
            name="test-service",
            base_url="http://test:8080",
            timeout=30.0
        )
        assert service_config.name == "test-service"
        print("✅ 服务配置模型正常")
        
        # 测试验证
        try:
            ServiceConfigModel(
                name="invalid",
                base_url="http://test:8080",
                timeout=-1.0  # 无效值
            )
            assert False, "应该抛出验证错误"
        except ValueError:
            print("✅ 服务配置验证正常")
        
        # 测试限流配置
        rate_config = RateLimitConfigModel(
            enabled=True,
            requests_per_minute=100
        )
        assert rate_config.enabled is True
        print("✅ 限流配置模型正常")
        
        print("🎉 模型类测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 模型类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始Gateway模块简化测试\n")
    
    tests = [
        ("熔断器功能", test_circuit_breaker_standalone),
        ("请求指标", test_request_metrics_standalone),
        ("工具函数", test_utility_functions_standalone),
        ("异常类", test_exceptions_standalone),
        ("模型类", test_models_standalone),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "="*40)
    print("📊 测试结果:")
    print("="*40)
    
    passed = sum(1 for _, result in results if result)
    failed = len(results) - passed
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} {status}")
    
    print("="*40)
    print(f"总计: {len(results)} | 通过: {passed} | 失败: {failed}")
    
    if failed == 0:
        print("\n🎊 所有测试通过!")
        return 0
    else:
        print(f"\n💥 有 {failed} 个测试失败!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
