from .util import create_message_chunks

MESSAGE = """
Hello World

# Mermaid

# GFM

## Autolink literals

www.example.com, https://example.com, and <EMAIL>.

## Strikethrough

~one~ or ~~two~~ tildes.

## Table

| a | b  |  c |  d  |
|---|----|----|-----|
| - | :- | -: | :-: |
| 1 |  2 |  3 |  4  |

```mermaid
classDiagram
Class01 <|-- AveryLongClass : Cool
<<Interface>> Class01
Class09 --> C2 : Where am I?



"""

error_message = [
    {
        "data": "{\"error_type\": \"ValueError\", \"error_message\": \"大模型无响应，请稍后再试。\"}",
        "is_last": True,
        "package_type": 3,
        "is_new_package": True,
        "is_error": True
    }
]


def create_error_message_chunks():
    return create_message_chunks(MESSAGE) + error_message
